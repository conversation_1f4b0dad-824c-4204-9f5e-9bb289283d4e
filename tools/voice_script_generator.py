#!/usr/bin/env python3
"""
语音脚本生成工具
基于Excel数据和现有文本脚本生成语音测试脚本
"""
import os
import sys
import pandas as pd
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.logger import log


class VoiceScriptGenerator:
    """语音脚本生成器"""
    
    def __init__(self):
        self.project_root = project_root
        self.excel_path = self.project_root / "data" / "query" / "query_v3.xlsx"
        self.text_scripts_dir = self.project_root / "testcases" / "test_ella"
        self.voice_scripts_dir = self.project_root / "testcases" / "test_voice_ella"
        
        # 类别到目录的映射（参考ella_test_generator_v2.py的逻辑）
        self.category_to_dir = {
            "模块耦合": "component_coupling",
            "dialogue": "dialogue", 
            "系统耦合": "system_coupling",
            "三方耦合": "third_coupling",
            "不支持指令": "unsupported_commands",
            "自身功能": "self_function"
        }
        
        # 默认语音设置
        self.default_voice_language = "en-US"
        self.default_voice_duration = 3.0
    
    def load_excel_data(self) -> pd.DataFrame:
        """加载Excel数据"""
        try:
            df = pd.read_excel(self.excel_path)
            log.info(f"成功加载Excel文件，共{len(df)}条记录")
            return df
        except Exception as e:
            log.error(f"加载Excel文件失败: {e}")
            raise
    
    def get_directory_from_category(self, category: str) -> str:
        """根据类别获取目录名"""
        return self.category_to_dir.get(category, "dialogue")  # 默认为dialogue
    
    def find_text_script(self, query: str, category: str) -> Optional[Path]:
        """查找对应的文本脚本"""
        dir_name = self.get_directory_from_category(category)
        text_dir = self.text_scripts_dir / dir_name
        
        if not text_dir.exists():
            log.warning(f"文本脚本目录不存在: {text_dir}")
            return None
        
        # 生成可能的文件名（参考ella_test_generator_v2.py的命名逻辑）
        method_name = self.generate_method_name(query)
        possible_filename = f"{method_name}.py"
        
        script_path = text_dir / possible_filename
        if script_path.exists():
            return script_path
        
        # 如果精确匹配不存在，尝试模糊匹配
        for file_path in text_dir.glob("*.py"):
            if "__init__" in file_path.name:
                continue
            # 简单的关键词匹配
            query_words = set(re.findall(r'\w+', query.lower()))
            filename_words = set(re.findall(r'\w+', file_path.stem.lower()))
            if query_words & filename_words:  # 有交集
                log.info(f"找到模糊匹配的文本脚本: {file_path}")
                return file_path
        
        log.warning(f"未找到匹配的文本脚本: {query} -> {possible_filename}")
        return None
    
    def generate_method_name(self, command: str) -> str:
        """生成方法名（参考ella_test_generator_v2.py）"""
        # 只保留字母和空格，去除数字、特殊字符
        letters_only = re.sub(r'[^a-zA-Z\s]', ' ', command)
        # 转换为小写并用下划线连接
        method_name = letters_only.lower().replace(" ", "_")
        # 移除连续的下划线
        while "__" in method_name:
            method_name = method_name.replace("__", "_")
        method_name = method_name.strip("_")
        
        # 如果处理后为空，使用默认名称
        if not method_name:
            method_name = "command"
        
        return f"test_{method_name}"
    
    def extract_expected_text_from_script(self, script_content: str) -> str:
        """从脚本内容中提取expected_text的值"""
        try:
            # 首先尝试匹配类属性中的 expected_text = [...]
            class_pattern = r'class\s+\w+.*?:\s*"""[^"]*"""[^@]*?expected_text\s*=\s*(\[.*?\])'
            match = re.search(class_pattern, script_content, re.DOTALL)
            if match:
                return match.group(1)

            # 如果类属性中没有，尝试匹配方法内的 expected_text = [...]
            method_pattern = r'expected_text\s*=\s*(\[.*?\])'
            match = re.search(method_pattern, script_content, re.DOTALL)
            if match:
                return match.group(1)

            # 如果都没找到，返回默认值
            return '["Done"]'
        except Exception as e:
            log.warning(f"提取expected_text失败: {e}")
            return '["Done"]'

    def has_class_attributes(self, script_content: str) -> bool:
        """检查脚本是否有类属性（command或expected_text）"""
        try:
            # 检查类定义后是否有属性定义
            class_pattern = r'class\s+\w+.*?:\s*"""[^"]*"""([^@]*?)@'
            match = re.search(class_pattern, script_content, re.DOTALL)
            if match:
                class_body = match.group(1)
                # 检查是否有command或expected_text属性
                return bool(re.search(r'(command|expected_text)\s*=', class_body))
            return False
        except Exception:
            return False

    def convert_text_to_voice_script(self, text_script_path: Path, query: str,
                                   voice_language: str = None,
                                   voice_duration: float = None) -> str:
        """将文本脚本转换为语音脚本"""
        if voice_language is None:
            voice_language = self.default_voice_language
        if voice_duration is None:
            voice_duration = self.default_voice_duration

        try:
            with open(text_script_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取原始脚本的expected_text值
            original_expected_text = self.extract_expected_text_from_script(content)
            has_class_attrs = self.has_class_attributes(content)

            # 更新文档字符串
            content = re.sub(r'"""[^"]*"""', f'"""\nElla语音助手语音指令测试\n"""', content, count=1)

            # 替换类名（添加Voice前缀）
            content = re.sub(r'class (TestElla\w+)', r'class \1Voice', content)

            # 替换方法名（添加voice前缀）
            method_pattern = r'def (test_\w+)\(self, ella_app\):'
            content = re.sub(method_pattern, r'def \1_voice(self, ella_app):', content)

            if has_class_attrs:
                # 如果原脚本有类属性，更新它们
                # 更新command值
                command_pattern = r'command = "[^"]*"'
                content = re.sub(command_pattern, f'command = "{query}"', content)

                # 更新expected_text值并添加语音属性
                expected_text_pattern = r'expected_text = \[.*?\]'
                replacement = f'''expected_text = {original_expected_text}
    voice_language = "{voice_language}"  # 语音语言
    voice_duration = {voice_duration}      # 语音持续时间'''
                content = re.sub(expected_text_pattern, replacement, content, flags=re.DOTALL)
            else:
                # 如果原脚本没有类属性，在类定义后添加它们
                class_pattern = r'(class \w+Voice\(SimpleEllaTest\):\s*"""[^"]*""")'
                class_attributes = f'''
    command = "{query}"
    expected_text = {original_expected_text}
    voice_language = "{voice_language}"  # 语音语言
    voice_duration = {voice_duration}      # 语音持续时间'''
                content = re.sub(class_pattern, r'\1' + class_attributes, content)

            # 移除可能存在的错误缩进的语音变量
            content = re.sub(r'\n\s+voice_language = self\.voice_language\n\s+voice_duration = self\.voice_duration', '', content)

            # 在方法开始处添加语音变量
            # 先处理 command = self.command 的情况
            if 'command = self.command' in content:
                pattern = r'(\s+command = self\.command)(\s*\n)'
                replacement = r'\1\n        voice_language = self.voice_language\n        voice_duration = self.voice_duration\2'
                content = re.sub(pattern, replacement, content)

            # 再处理 command = "..." 的情况
            elif re.search(r'command = "[^"]*"', content):
                pattern = r'(\s+command = "[^"]*")(\s*\n)'
                replacement = r'\1\n        voice_language = self.voice_language\n        voice_duration = self.voice_duration\2'
                content = re.sub(pattern, replacement, content)

            # 修改simple_command_test调用，添加语音参数
            simple_command_pattern = r'(initial_status, final_status, response_text, files_status = self\.simple_command_test\(\s*ella_app,\s*command)([^)]*)\)'
            voice_params = r'\1,\n                is_voice=True,  # 语音标识为True\n                voice_duration=voice_duration,\n                voice_language=voice_language\2)'

            content = re.sub(simple_command_pattern, voice_params, content)

            # 修改allure步骤描述
            step_pattern = r'with allure\.step\(f"执行命令: \{command\}"\):'
            content = re.sub(step_pattern,
                           'with allure.step(f"执行语音命令: {command} (语言: {voice_language})"):',
                           content)

            # 添加语音信息到测试结果记录
            summary_pattern = r'(summary = self\.create_test_summary\(command, initial_status, final_status, response_text\))'
            voice_info = r'''\1
            # 添加语音相关信息
            voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒"""
            summary += voice_info'''

            content = re.sub(summary_pattern, voice_info, content)

            return content

        except Exception as e:
            log.error(f"转换文本脚本失败: {e}")
            raise
    
    def generate_voice_script(self, query: str, category: str, 
                            voice_language: str = None, 
                            voice_duration: float = None) -> Optional[str]:
        """生成单个语音脚本"""
        # 查找对应的文本脚本
        text_script_path = self.find_text_script(query, category)
        if not text_script_path:
            log.warning(f"跳过生成语音脚本，未找到文本脚本: {query}")
            return None
        
        # 确定输出目录
        dir_name = self.get_directory_from_category(category)
        voice_dir = self.voice_scripts_dir / dir_name
        voice_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        method_name = self.generate_method_name(query)
        voice_filename = f"test_voice_{method_name[5:]}.py"  # 移除test_前缀再添加test_voice_
        voice_script_path = voice_dir / voice_filename
        
        # 转换脚本内容
        voice_content = self.convert_text_to_voice_script(
            text_script_path, query, voice_language, voice_duration
        )
        
        # 写入文件
        with open(voice_script_path, 'w', encoding='utf-8') as f:
            f.write(voice_content)
        
        log.info(f"✅ 生成语音脚本: {voice_script_path}")
        return str(voice_script_path)
    
    def generate_all_voice_scripts(self) -> List[str]:
        """生成所有语音脚本"""
        df = self.load_excel_data()
        generated_files = []
        
        for index, row in df.iterrows():
            query = row.get('query', '').strip()
            category = row.get('类别', '').strip()
            
            if not query:
                log.warning(f"跳过第{index+1}行，query为空")
                continue
            
            try:
                file_path = self.generate_voice_script(query, category)
                if file_path:
                    generated_files.append(file_path)
                    print(f"✅ {query} -> {category} -> {file_path}")
                else:
                    print(f"❌ 跳过: {query} (未找到对应文本脚本)")
            except Exception as e:
                print(f"❌ 生成失败 '{query}': {e}")
                log.error(f"生成语音脚本失败: {query}, 错误: {e}")
        
        return generated_files


def main():
    """主函数"""
    print("🚀 开始生成语音脚本...")
    
    generator = VoiceScriptGenerator()
    generated_files = generator.generate_all_voice_scripts()
    
    print(f"\n🎉 语音脚本生成完成！")
    print(f"📊 总共生成 {len(generated_files)} 个语音脚本")
    print(f"📁 输出目录: {generator.voice_scripts_dir}")
    
    if generated_files:
        print("\n📝 生成的文件列表:")
        for file_path in generated_files:
            print(f"  - {file_path}")


if __name__ == "__main__":
    main()
