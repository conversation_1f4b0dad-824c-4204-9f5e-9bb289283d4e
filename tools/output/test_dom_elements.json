{"timestamp": "2025-08-27T21:15:56.166711", "device_info": {"serial": "13764254B4001229", "sdk": 35, "brand": "TECNO", "model": "TECNO CM8", "arch": "arm64-v8a", "version": 15}, "total_clickable_elements": 67, "elements": [{"xpath": "hierarchy/node/node/node/node/node/node/node/node/node", "class": "android.widget.HorizontalScrollView", "resource_id": "com.transsion.aivoiceassistant:id/help_main_tab", "text": "", "content_desc": "", "bounds": "[42,123][620,291]", "clickable": "false", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 42, "top": 123, "right": 620, "bottom": 291, "center_x": 331, "center_y": 207, "width": 578, "height": 168}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.LinearLayout", "resource_id": "", "text": "", "content_desc": "", "bounds": "[42,123][385,291]", "clickable": "false", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "true", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 42, "top": 123, "right": 385, "bottom": 291, "center_x": 213, "center_y": 207, "width": 343, "height": 168}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.LinearLayout", "resource_id": "", "text": "", "content_desc": "", "bounds": "[385,123][620,291]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "false", "coordinates": {"left": 385, "top": 123, "right": 620, "bottom": 291, "center_x": 502, "center_y": 207, "width": 235, "height": 168}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "com.transsion.aivoiceassistant:id/iv_user", "text": "", "content_desc": "", "bounds": "[948,171][1020,243]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "false", "coordinates": {"left": 948, "top": 171, "right": 1020, "bottom": 243, "center_x": 984, "center_y": 207, "width": 72, "height": 72}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node", "class": "androidx.viewpager.widget.ViewPager", "resource_id": "com.transsion.aivoiceassistant:id/help_main_viewpager", "text": "", "content_desc": "", "bounds": "[0,291][1080,2400]", "clickable": "false", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "true", "coordinates": {"left": 0, "top": 291, "right": 1080, "bottom": 2400, "center_x": 540, "center_y": 1345, "width": 1080, "height": 2109}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node", "class": "androidx.recyclerview.widget.RecyclerView", "resource_id": "com.transsion.aivoiceassistant:id/rv_dialogue", "text": "", "content_desc": "", "bounds": "[0,384][1080,1213]", "clickable": "false", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "true", "coordinates": {"left": 0, "top": 384, "right": 1080, "bottom": 1213, "center_x": 540, "center_y": 798, "width": 1080, "height": 829}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.LinearLayout", "resource_id": "com.transsion.aivoiceassistant:id/check_area", "text": "", "content_desc": "", "bounds": "[48,384][1032,572]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 48, "top": 384, "right": 1032, "bottom": 572, "center_x": 540, "center_y": 478, "width": 984, "height": 188}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.view.ViewGroup", "resource_id": "com.transsion.aivoiceassistant:id/cl_content", "text": "", "content_desc": "", "bounds": "[48,384][734,572]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 48, "top": 384, "right": 734, "bottom": 572, "center_x": 391, "center_y": 478, "width": 686, "height": 188}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "com.transsion.aivoiceassistant:id/iv_tts_play", "text": "", "content_desc": "", "bounds": "[614,455][686,527]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "false", "coordinates": {"left": 614, "top": 455, "right": 686, "bottom": 527, "center_x": 650, "center_y": 491, "width": 72, "height": 72}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.LinearLayout", "resource_id": "com.transsion.aivoiceassistant:id/check_area", "text": "", "content_desc": "", "bounds": "[48,602][1032,953]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "false", "coordinates": {"left": 48, "top": 602, "right": 1032, "bottom": 953, "center_x": 540, "center_y": 777, "width": 984, "height": 351}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.LinearLayout", "resource_id": "com.transsion.aivoiceassistant:id/ll_content", "text": "", "content_desc": "", "bounds": "[48,632][1032,782]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 48, "top": 632, "right": 1032, "bottom": 782, "center_x": 540, "center_y": 707, "width": 984, "height": 150}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.RelativeLayout", "resource_id": "com.transsion.aivoiceassistant:id/device_control", "text": "", "content_desc": "", "bounds": "[48,632][1032,782]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 48, "top": 632, "right": 1032, "bottom": 782, "center_x": 540, "center_y": 707, "width": 984, "height": 150}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.Switch", "resource_id": "com.transsion.aivoiceassistant:id/function_control", "text": "", "content_desc": "", "bounds": "[864,674][984,740]", "clickable": "false", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "true", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "false", "coordinates": {"left": 864, "top": 674, "right": 984, "bottom": 740, "center_x": 924, "center_y": 707, "width": 120, "height": 66}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.LinearLayout", "resource_id": "com.transsion.aivoiceassistant:id/ll_bottom", "text": "", "content_desc": "", "bounds": "[48,812][1032,932]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "1", "scrollable": "false", "coordinates": {"left": 48, "top": 812, "right": 1032, "bottom": 932, "center_x": 540, "center_y": 872, "width": 984, "height": 120}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "com.transsion.aivoiceassistant:id/iv_like", "text": "", "content_desc": "", "bounds": "[90,992][156,1058]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 90, "top": 992, "right": 156, "bottom": 1058, "center_x": 123, "center_y": 1025, "width": 66, "height": 66}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "com.transsion.aivoiceassistant:id/iv_unlike", "text": "", "content_desc": "", "bounds": "[231,992][297,1058]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "2", "scrollable": "false", "coordinates": {"left": 231, "top": 992, "right": 297, "bottom": 1058, "center_x": 264, "center_y": 1025, "width": 66, "height": 66}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.TextView", "resource_id": "", "text": "Dim the screen", "content_desc": "", "bounds": "[48,1109][460,1213]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 48, "top": 1109, "right": 460, "bottom": 1213, "center_x": 254, "center_y": 1161, "width": 412, "height": 104}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.view.ViewGroup", "resource_id": "com.transsion.aivoiceassistant:id/rl_root", "text": "", "content_desc": "", "bounds": "[0,1213][1080,2400]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 0, "top": 1213, "right": 1080, "bottom": 2400, "center_x": 540, "center_y": 1806, "width": 1080, "height": 1187}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.TextView", "resource_id": "com.transsion.aivoiceassistant:id/btn_deep_seek", "text": "DeepSeek-R1", "content_desc": "", "bounds": "[48,1270][352,1354]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 48, "top": 1270, "right": 352, "bottom": 1354, "center_x": 200, "center_y": 1312, "width": 304, "height": 84}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.transsion.aivoiceassistant:id/rl_input", "text": "", "content_desc": "", "bounds": "[0,1354][1080,1589]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "2", "scrollable": "false", "coordinates": {"left": 0, "top": 1354, "right": 1080, "bottom": 1589, "center_x": 540, "center_y": 1471, "width": 1080, "height": 235}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.transsion.aivoiceassistant:id/lv_ip_anim_view", "text": "", "content_desc": "", "bounds": "[60,1393][192,1525]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 60, "top": 1393, "right": 192, "bottom": 1525, "center_x": 126, "center_y": 1459, "width": 132, "height": 132}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.EditText", "resource_id": "com.transsion.aivoiceassistant:id/et_input", "text": "", "content_desc": "", "bounds": "[192,1413][792,1529]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 192, "top": 1413, "right": 792, "bottom": 1529, "center_x": 492, "center_y": 1471, "width": 600, "height": 116}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "com.transsion.aivoiceassistant:id/btn_voice", "text": "", "content_desc": "", "bounds": "[792,1423][888,1519]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "0", "scrollable": "false", "coordinates": {"left": 792, "top": 1423, "right": 888, "bottom": 1519, "center_x": 840, "center_y": 1471, "width": 96, "height": 96}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "com.transsion.aivoiceassistant:id/btn_expand", "text": "", "content_desc": "", "bounds": "[912,1423][1008,1519]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.transsion.aivoiceassistant", "index": "3", "scrollable": "false", "coordinates": {"left": 912, "top": 1423, "right": 1008, "bottom": 1519, "center_x": 960, "center_y": 1471, "width": 96, "height": 96}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_header_access_points_menu", "text": "", "content_desc": "Open features menu", "bounds": "[6,1589][122,1710]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "1", "scrollable": "false", "coordinates": {"left": 6, "top": 1589, "right": 122, "bottom": 1710, "center_x": 64, "center_y": 1649, "width": 116, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "", "text": "", "content_desc": "Sticker Keyboard", "bounds": "[145,1589][303,1710]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 145, "top": 1589, "right": 303, "bottom": 1710, "center_x": 224, "center_y": 1649, "width": 158, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "", "text": "", "content_desc": "GIF Keyboard", "bounds": "[303,1589][461,1710]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "1", "scrollable": "false", "coordinates": {"left": 303, "top": 1589, "right": 461, "bottom": 1710, "center_x": 382, "center_y": 1649, "width": 158, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "", "text": "", "content_desc": "Clipboard", "bounds": "[461,1589][619,1710]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "2", "scrollable": "false", "coordinates": {"left": 461, "top": 1589, "right": 619, "bottom": 1710, "center_x": 540, "center_y": 1649, "width": 158, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "", "text": "", "content_desc": "Settings", "bounds": "[619,1589][777,1710]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "3", "scrollable": "false", "coordinates": {"left": 619, "top": 1589, "right": 777, "bottom": 1710, "center_x": 698, "center_y": 1649, "width": 158, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "", "text": "", "content_desc": "Theme settings", "bounds": "[777,1589][935,1710]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "4", "scrollable": "false", "coordinates": {"left": 777, "top": 1589, "right": 935, "bottom": 1710, "center_x": 856, "center_y": 1649, "width": 158, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_header_power_key", "text": "", "content_desc": "Use voice typing", "bounds": "[958,1589][1074,1710]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "3", "scrollable": "false", "coordinates": {"left": 958, "top": 1589, "right": 1074, "bottom": 1710, "center_x": 1016, "center_y": 1649, "width": 116, "height": 121}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_0", "text": "", "content_desc": "q", "bounds": "[6,1732][112,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 6, "top": 1732, "right": 112, "bottom": 1870, "center_x": 59, "center_y": 1801, "width": 106, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_1", "text": "", "content_desc": "w", "bounds": "[112,1732][218,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "1", "scrollable": "false", "coordinates": {"left": 112, "top": 1732, "right": 218, "bottom": 1870, "center_x": 165, "center_y": 1801, "width": 106, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_2", "text": "", "content_desc": "e", "bounds": "[218,1732][325,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "2", "scrollable": "false", "coordinates": {"left": 218, "top": 1732, "right": 325, "bottom": 1870, "center_x": 271, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_3", "text": "", "content_desc": "r", "bounds": "[325,1732][432,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "3", "scrollable": "false", "coordinates": {"left": 325, "top": 1732, "right": 432, "bottom": 1870, "center_x": 378, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_4", "text": "", "content_desc": "t", "bounds": "[432,1732][539,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "4", "scrollable": "false", "coordinates": {"left": 432, "top": 1732, "right": 539, "bottom": 1870, "center_x": 485, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_5", "text": "", "content_desc": "y", "bounds": "[539,1732][646,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "5", "scrollable": "false", "coordinates": {"left": 539, "top": 1732, "right": 646, "bottom": 1870, "center_x": 592, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_6", "text": "", "content_desc": "u", "bounds": "[646,1732][753,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "6", "scrollable": "false", "coordinates": {"left": 646, "top": 1732, "right": 753, "bottom": 1870, "center_x": 699, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_7", "text": "", "content_desc": "i", "bounds": "[753,1732][860,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "7", "scrollable": "false", "coordinates": {"left": 753, "top": 1732, "right": 860, "bottom": 1870, "center_x": 806, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_8", "text": "", "content_desc": "o", "bounds": "[860,1732][967,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "8", "scrollable": "false", "coordinates": {"left": 860, "top": 1732, "right": 967, "bottom": 1870, "center_x": 913, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_0_9", "text": "", "content_desc": "p", "bounds": "[967,1732][1074,1870]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "9", "scrollable": "false", "coordinates": {"left": 967, "top": 1732, "right": 1074, "bottom": 1870, "center_x": 1020, "center_y": 1801, "width": 107, "height": 138}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_0", "text": "", "content_desc": "a", "bounds": "[6,1870][166,2009]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 6, "top": 1870, "right": 166, "bottom": 2009, "center_x": 86, "center_y": 1939, "width": 160, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_1", "text": "", "content_desc": "s", "bounds": "[166,1870][272,2009]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "1", "scrollable": "false", "coordinates": {"left": 166, "top": 1870, "right": 272, "bottom": 2009, "center_x": 219, "center_y": 1939, "width": 106, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_2", "text": "", "content_desc": "d", "bounds": "[272,1870][378,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "2", "scrollable": "false", "coordinates": {"left": 272, "top": 1870, "right": 378, "bottom": 2009, "center_x": 325, "center_y": 1939, "width": 106, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_3", "text": "", "content_desc": "f", "bounds": "[378,1870][485,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "3", "scrollable": "false", "coordinates": {"left": 378, "top": 1870, "right": 485, "bottom": 2009, "center_x": 431, "center_y": 1939, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_4", "text": "", "content_desc": "g", "bounds": "[485,1870][592,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "4", "scrollable": "false", "coordinates": {"left": 485, "top": 1870, "right": 592, "bottom": 2009, "center_x": 538, "center_y": 1939, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_5", "text": "", "content_desc": "h", "bounds": "[592,1870][699,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "5", "scrollable": "false", "coordinates": {"left": 592, "top": 1870, "right": 699, "bottom": 2009, "center_x": 645, "center_y": 1939, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_6", "text": "", "content_desc": "j", "bounds": "[699,1870][806,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "6", "scrollable": "false", "coordinates": {"left": 699, "top": 1870, "right": 806, "bottom": 2009, "center_x": 752, "center_y": 1939, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_7", "text": "", "content_desc": "k", "bounds": "[806,1870][913,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "7", "scrollable": "false", "coordinates": {"left": 806, "top": 1870, "right": 913, "bottom": 2009, "center_x": 859, "center_y": 1939, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_1_8", "text": "", "content_desc": "l", "bounds": "[913,1870][1074,2009]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "8", "scrollable": "false", "coordinates": {"left": 913, "top": 1870, "right": 1074, "bottom": 2009, "center_x": 993, "center_y": 1939, "width": 161, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_shift", "text": "", "content_desc": "Shift", "bounds": "[6,2009][166,2148]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 6, "top": 2009, "right": 166, "bottom": 2148, "center_x": 86, "center_y": 2078, "width": 160, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_1", "text": "", "content_desc": "z", "bounds": "[166,2009][272,2148]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "1", "scrollable": "false", "coordinates": {"left": 166, "top": 2009, "right": 272, "bottom": 2148, "center_x": 219, "center_y": 2078, "width": 106, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_2", "text": "", "content_desc": "x", "bounds": "[272,2009][378,2148]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "2", "scrollable": "false", "coordinates": {"left": 272, "top": 2009, "right": 378, "bottom": 2148, "center_x": 325, "center_y": 2078, "width": 106, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_3", "text": "", "content_desc": "c", "bounds": "[378,2009][485,2148]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "3", "scrollable": "false", "coordinates": {"left": 378, "top": 2009, "right": 485, "bottom": 2148, "center_x": 431, "center_y": 2078, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_4", "text": "", "content_desc": "v", "bounds": "[485,2009][592,2148]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "4", "scrollable": "false", "coordinates": {"left": 485, "top": 2009, "right": 592, "bottom": 2148, "center_x": 538, "center_y": 2078, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_5", "text": "", "content_desc": "b", "bounds": "[592,2009][699,2148]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "5", "scrollable": "false", "coordinates": {"left": 592, "top": 2009, "right": 699, "bottom": 2148, "center_x": 645, "center_y": 2078, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_6", "text": "", "content_desc": "n", "bounds": "[699,2009][806,2148]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "6", "scrollable": "false", "coordinates": {"left": 699, "top": 2009, "right": 806, "bottom": 2148, "center_x": 752, "center_y": 2078, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_2_7", "text": "", "content_desc": "m", "bounds": "[806,2009][913,2148]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "7", "scrollable": "false", "coordinates": {"left": 806, "top": 2009, "right": 913, "bottom": 2148, "center_x": 859, "center_y": 2078, "width": 107, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_del", "text": "", "content_desc": "Delete", "bounds": "[913,2009][1074,2148]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "8", "scrollable": "false", "coordinates": {"left": 913, "top": 2009, "right": 1074, "bottom": 2148, "center_x": 993, "center_y": 2078, "width": 161, "height": 139}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_switch_to_symbol", "text": "", "content_desc": "Symbol keyboard", "bounds": "[6,2148][166,2290]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 6, "top": 2148, "right": 166, "bottom": 2290, "center_x": 86, "center_y": 2219, "width": 160, "height": 142}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_bottom_symbol_1", "text": "", "content_desc": ",", "bounds": "[166,2148][272,2290]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "1", "scrollable": "false", "coordinates": {"left": 166, "top": 2148, "right": 272, "bottom": 2290, "center_x": 219, "center_y": 2219, "width": 106, "height": 142}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_switch_to_next_language", "text": "", "content_desc": "Next language", "bounds": "[272,2148][378,2290]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 272, "top": 2148, "right": 378, "bottom": 2290, "center_x": 325, "center_y": 2219, "width": 106, "height": 142}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_space", "text": "", "content_desc": "Space", "bounds": "[378,2148][806,2290]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 378, "top": 2148, "right": 806, "bottom": 2290, "center_x": 592, "center_y": 2219, "width": 428, "height": 142}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_bottom_symbol_2", "text": "", "content_desc": ".", "bounds": "[806,2148][913,2290]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "3", "scrollable": "false", "coordinates": {"left": 806, "top": 2148, "right": 913, "bottom": 2290, "center_x": 859, "center_y": 2219, "width": 107, "height": 142}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node/node/node/node/node/node", "class": "android.widget.FrameLayout", "resource_id": "com.google.android.inputmethod.latin:id/key_pos_ime_action", "text": "", "content_desc": "Done", "bounds": "[913,2148][1074,2290]", "clickable": "true", "focusable": "true", "long_clickable": "true", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "4", "scrollable": "false", "coordinates": {"left": 913, "top": 2148, "right": 1074, "bottom": 2290, "center_x": 993, "center_y": 2219, "width": 161, "height": 142}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "android:id/input_method_nav_back", "text": "", "content_desc": "Back", "bounds": "[22,2290][214,2400]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 22, "top": 2290, "right": 214, "bottom": 2400, "center_x": 118, "center_y": 2345, "width": 192, "height": 110}}, {"xpath": "hierarchy/node/node/node/node/node/node/node/node/node", "class": "android.widget.ImageView", "resource_id": "android:id/input_method_nav_ime_switcher", "text": "", "content_desc": "Switch input method", "bounds": "[866,2290][1058,2400]", "clickable": "true", "focusable": "true", "long_clickable": "false", "enabled": "true", "selected": "false", "checked": "false", "package": "com.google.android.inputmethod.latin", "index": "0", "scrollable": "false", "coordinates": {"left": 866, "top": 2290, "right": 1058, "bottom": 2400, "center_x": 962, "center_y": 2345, "width": 192, "height": 110}}]}