"""
快速DOM提取工具
简化版的Android DOM元素提取工具，专门用于快速提取Ella语音助手的UI元素

使用方法:
python tools/quick_dom_extract.py
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from tools.android_dom_extractor import AndroidDOMExtractor
    from core.logger import log
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保已安装依赖: pip install uiautomator2")
    sys.exit(1)


def quick_extract_ella_elements():
    """快速提取Ella语音助手的UI元素"""
    print("🚀 快速DOM提取工具")
    print("=" * 50)
    
    # 配置参数
    package_name = "com.transsion.aivoiceassistant"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"ella_dom_elements_{timestamp}.json"
    
    print(f"📱 目标应用: {package_name}")
    print(f"📄 输出文件: {output_file}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    # 创建提取器
    extractor = AndroidDOMExtractor()
    
    try:
        # 执行提取
        print("🔄 正在执行DOM元素提取...")
        success = extractor.extract_dom_elements(
            package_name=package_name,
            output_file=output_file,
            wait_time=3,
            save_xml=True
        )
        
        if success:
            print("\n✅ 提取成功！")
            print(f"📁 JSON文件: tools/output/{output_file}")
            print(f"📄 XML文件: temp/page_source_*.xml")
            
            # 显示简要统计信息
            try:
                import json
                output_path = Path("tools/output") / output_file
                with open(output_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"\n📊 统计信息:")
                print(f"   可点击元素数量: {data.get('total_clickable_elements', 0)}")
                print(f"   设备信息: {data.get('device_info', {}).get('brand', 'Unknown')} {data.get('device_info', {}).get('model', 'Unknown')}")
                
                # 显示前几个元素
                elements = data.get('elements', [])
                if elements:
                    print(f"\n🎯 前3个可点击元素:")
                    for i, element in enumerate(elements[:3]):
                        text = element.get('text', '').strip()
                        class_name = element.get('class', '').split('.')[-1]  # 只显示类名最后部分
                        resource_id = element.get('resource_id', '').split(':id/')[-1] if ':id/' in element.get('resource_id', '') else ''
                        
                        print(f"   {i+1}. {class_name}")
                        if text:
                            print(f"      文本: {text}")
                        if resource_id:
                            print(f"      ID: {resource_id}")
                        print()
                
            except Exception as e:
                print(f"⚠️  读取统计信息失败: {e}")
            
        else:
            print("\n❌ 提取失败！")
            print("请检查:")
            print("  - 设备是否正确连接")
            print("  - Ella应用是否已安装")
            print("  - USB调试是否已开启")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        return False
    
    return True


def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查adb连接
    try:
        import subprocess
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            devices = [line for line in result.stdout.split('\n') if '\tdevice' in line]
            if devices:
                print(f"✅ 发现 {len(devices)} 个连接的设备")
                for device in devices:
                    print(f"   - {device.split('\t')[0]}")
            else:
                print("⚠️  未发现连接的设备")
                print("请确保:")
                print("  - 设备已通过USB连接")
                print("  - 已开启USB调试")
                print("  - 已授权计算机调试")
                return False
        else:
            print("❌ ADB命令执行失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到ADB命令")
        print("请安装Android SDK Platform Tools")
        return False
    except Exception as e:
        print(f"❌ 检查ADB失败: {e}")
        return False
    
    # 检查UIAutomator2
    try:
        import uiautomator2 as u2
        print("✅ UIAutomator2模块可用")
    except ImportError:
        print("❌ UIAutomator2模块未安装")
        print("请执行: pip install uiautomator2")
        return False
    
    return True


def main():
    """主函数"""
    print("🤖 Ella DOM元素快速提取工具")
    print("=" * 50)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请解决上述问题后重试")
        return
    
    print("\n" + "=" * 50)
    
    # 执行快速提取
    success = quick_extract_ella_elements()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 任务完成！")
        print("\n💡 提示:")
        print("  - 可以使用JSON文件进行UI自动化测试")
        print("  - XML文件可用于调试和分析")
        print("  - 如需提取其他应用，请使用完整版工具:")
        print("    python tools/android_dom_extractor.py --package <包名>")
    else:
        print("❌ 任务失败！")
        print("\n🔧 故障排除:")
        print("  1. 检查设备连接: adb devices")
        print("  2. 重启ADB服务: adb kill-server && adb start-server")
        print("  3. 重新安装UIAutomator2: python -m uiautomator2 init")
        print("  4. 查看详细日志: logs/test.log")


if __name__ == "__main__":
    main()
