"""
Android DOM提取工具使用示例
展示各种使用场景和功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from tools.android_dom_extractor import AndroidDOMExtractor
    from tools.advanced_dom_extractor import AdvancedDOMExtractor
    from core.logger import log
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)


def example_basic_extraction():
    """示例1: 基本DOM提取"""
    print("=" * 60)
    print("示例1: 基本DOM提取")
    print("=" * 60)
    
    extractor = AndroidDOMExtractor()
    
    # 提取Ella语音助手的DOM元素
    success = extractor.extract_dom_elements(
        package_name="com.transsion.aivoiceassistant",
        output_file="example_basic_ella.json",
        wait_time=3,
        save_xml=True
    )
    
    if success:
        print("✅ 基本提取示例完成")
        
        # 读取并分析结果
        output_path = Path("tools/output/example_basic_ella.json")
        if output_path.exists():
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📊 提取结果:")
            print(f"   可点击元素: {data.get('total_clickable_elements', 0)}")
            print(f"   设备型号: {data.get('device_info', {}).get('model', 'Unknown')}")
    else:
        print("❌ 基本提取示例失败")


def example_advanced_extraction():
    """示例2: 高级DOM提取"""
    print("\n" + "=" * 60)
    print("示例2: 高级DOM提取（带截图和分类）")
    print("=" * 60)
    
    extractor = AdvancedDOMExtractor()
    
    # 高级提取，包含截图和元素分类
    success = extractor.extract_advanced_dom_elements(
        package_name="com.transsion.aivoiceassistant",
        output_file="example_advanced_ella.json",
        enable_screenshot=True,
        enable_interaction_test=False,  # 为了安全，不启用交互测试
        filter_keywords=["开始", "设置", "语音", "对话"]
    )
    
    if success:
        print("✅ 高级提取示例完成")
        
        # 分析高级报告
        output_path = Path("tools/output/example_advanced_ella.json")
        if output_path.exists():
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📊 高级分析结果:")
            summary = data.get('summary', {})
            print(f"   总元素数: {summary.get('total_elements', 0)}")
            
            categorized_counts = summary.get('categorized_counts', {})
            for category, count in categorized_counts.items():
                if count > 0:
                    print(f"   {category}: {count}")
            
            if summary.get('screenshot_path'):
                print(f"   截图已保存: {summary.get('screenshot_path')}")
    else:
        print("❌ 高级提取示例失败")


def example_element_filtering():
    """示例3: 元素过滤和分析"""
    print("\n" + "=" * 60)
    print("示例3: 元素过滤和分析")
    print("=" * 60)
    
    extractor = AndroidDOMExtractor()
    
    # 连接设备并获取页面源码
    if not extractor.connect_device():
        print("❌ 设备连接失败")
        return
    
    if not extractor.launch_app("com.transsion.aivoiceassistant"):
        print("❌ 启动应用失败")
        return
    
    xml_content = extractor.get_page_source()
    if not xml_content:
        print("❌ 获取页面源码失败")
        return
    
    # 解析元素
    elements = extractor.parse_xml_elements(xml_content)
    print(f"📊 解析结果: 找到 {len(elements)} 个可点击元素")
    
    # 分析元素类型
    class_stats = {}
    text_elements = []
    button_elements = []
    
    for element in elements:
        class_name = element.get('class', 'Unknown')
        class_stats[class_name] = class_stats.get(class_name, 0) + 1
        
        if element.get('text'):
            text_elements.append(element)
        
        if 'button' in class_name.lower():
            button_elements.append(element)
    
    print(f"\n🔍 元素类型分析:")
    for class_name, count in sorted(class_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"   {class_name.split('.')[-1]}: {count}")
    
    print(f"\n📝 文本元素分析:")
    print(f"   有文本的元素: {len(text_elements)}")
    print(f"   按钮类元素: {len(button_elements)}")
    
    # 显示一些有趣的元素
    if text_elements:
        print(f"\n💡 有文本的元素示例:")
        for i, element in enumerate(text_elements[:3]):
            text = element.get('text', '').strip()
            if text:
                print(f"   {i+1}. \"{text}\" ({element.get('class', '').split('.')[-1]})")


def example_batch_processing():
    """示例4: 批量处理多个应用"""
    print("\n" + "=" * 60)
    print("示例4: 批量处理多个应用")
    print("=" * 60)
    
    # 定义要处理的应用列表
    apps = [
        {
            "name": "Ella语音助手",
            "package": "com.transsion.aivoiceassistant",
            "keywords": ["开始", "语音", "设置"]
        },
        {
            "name": "系统设置",
            "package": "com.android.settings",
            "keywords": ["设置", "开关", "选项"]
        }
    ]
    
    extractor = AndroidDOMExtractor()
    results = []
    
    for app in apps:
        print(f"\n🔄 处理应用: {app['name']}")
        
        try:
            # 生成输出文件名
            timestamp = time.strftime("%H%M%S")
            output_file = f"batch_{app['package'].split('.')[-1]}_{timestamp}.json"
            
            # 执行提取
            success = extractor.extract_dom_elements(
                package_name=app['package'],
                output_file=output_file,
                wait_time=2,
                save_xml=False  # 批量处理时不保存XML
            )
            
            if success:
                print(f"   ✅ {app['name']} 处理完成")
                results.append({
                    "app": app['name'],
                    "package": app['package'],
                    "output_file": output_file,
                    "status": "success"
                })
            else:
                print(f"   ❌ {app['name']} 处理失败")
                results.append({
                    "app": app['name'],
                    "package": app['package'],
                    "status": "failed"
                })
            
            # 短暂等待
            time.sleep(1)
            
        except Exception as e:
            print(f"   ❌ {app['name']} 处理异常: {e}")
            results.append({
                "app": app['name'],
                "package": app['package'],
                "status": "error",
                "error": str(e)
            })
    
    # 保存批量处理结果
    batch_result = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_apps": len(apps),
        "successful": len([r for r in results if r['status'] == 'success']),
        "failed": len([r for r in results if r['status'] != 'success']),
        "results": results
    }
    
    batch_output = Path("tools/output/batch_processing_result.json")
    with open(batch_output, 'w', encoding='utf-8') as f:
        json.dump(batch_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 批量处理完成:")
    print(f"   成功: {batch_result['successful']}")
    print(f"   失败: {batch_result['failed']}")
    print(f"   结果文件: {batch_output}")


def example_custom_analysis():
    """示例5: 自定义分析"""
    print("\n" + "=" * 60)
    print("示例5: 自定义分析")
    print("=" * 60)
    
    # 读取之前生成的数据进行分析
    output_dir = Path("tools/output")
    json_files = list(output_dir.glob("*.json"))
    
    if not json_files:
        print("❌ 未找到可分析的JSON文件")
        return
    
    print(f"📁 找到 {len(json_files)} 个JSON文件")
    
    # 分析最新的文件
    latest_file = max(json_files, key=lambda f: f.stat().st_mtime)
    print(f"🔍 分析文件: {latest_file.name}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        elements = data.get('elements', [])
        if not elements:
            print("❌ 文件中没有元素数据")
            return
        
        # 自定义分析
        analysis = {
            "总元素数": len(elements),
            "有文本元素": len([e for e in elements if e.get('text')]),
            "有资源ID元素": len([e for e in elements if e.get('resource_id')]),
            "可长按元素": len([e for e in elements if e.get('long_clickable') == 'true']),
            "可滚动元素": len([e for e in elements if e.get('scrollable') == 'true']),
        }
        
        print(f"\n📊 自定义分析结果:")
        for key, value in analysis.items():
            print(f"   {key}: {value}")
        
        # 查找特殊元素
        special_elements = []
        for element in elements:
            text = element.get('text', '').lower()
            if any(keyword in text for keyword in ['开始', '确定', '设置', '取消']):
                special_elements.append(element)
        
        if special_elements:
            print(f"\n🎯 找到 {len(special_elements)} 个关键元素:")
            for i, element in enumerate(special_elements[:3]):
                print(f"   {i+1}. \"{element.get('text', '')}\" - {element.get('class', '').split('.')[-1]}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("🤖 Android DOM提取工具使用示例")
    print("=" * 60)
    
    try:
        # 示例1: 基本提取
        example_basic_extraction()
        
        # 示例2: 高级提取
        example_advanced_extraction()
        
        # 示例3: 元素过滤
        example_element_filtering()
        
        # 示例4: 批量处理
        example_batch_processing()
        
        # 示例5: 自定义分析
        example_custom_analysis()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("📁 查看 tools/output/ 目录获取生成的文件")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️  示例运行被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
