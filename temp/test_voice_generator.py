#!/usr/bin/env python3
"""
语音脚本生成工具测试脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tools.voice_script_generator import VoiceScriptGenerator


def test_single_generation():
    """测试单个语音脚本生成"""
    print("🧪 测试单个语音脚本生成...")
    
    generator = VoiceScriptGenerator()
    
    # 测试用例
    test_cases = [
        ("play music", "模块耦合"),
        ("global gdp trends", "dialogue"),
        ("open bluetooth", "系统耦合")
    ]
    
    for query, category in test_cases:
        print(f"\n测试: {query} ({category})")
        try:
            file_path = generator.generate_voice_script(query, category)
            if file_path:
                print(f"✅ 成功生成: {file_path}")
            else:
                print(f"❌ 生成失败: 未找到对应文本脚本")
        except Exception as e:
            print(f"❌ 生成失败: {e}")


def test_excel_loading():
    """测试Excel数据加载"""
    print("🧪 测试Excel数据加载...")
    
    generator = VoiceScriptGenerator()
    
    try:
        df = generator.load_excel_data()
        print(f"✅ 成功加载Excel文件，共{len(df)}条记录")
        print(f"📊 列名: {df.columns.tolist()}")
        print(f"📝 前3条数据:")
        for i, row in df.head(3).iterrows():
            query = row.get('query', 'N/A')
            category = row.get('类别', 'N/A')
            print(f"  {i+1}. {query} ({category})")
    except Exception as e:
        print(f"❌ 加载失败: {e}")


def test_directory_mapping():
    """测试类别到目录的映射"""
    print("🧪 测试类别到目录映射...")
    
    generator = VoiceScriptGenerator()
    
    test_categories = ["模块耦合", "dialogue", "系统耦合", "三方耦合", "不支持指令", "未知类别"]
    
    for category in test_categories:
        dir_name = generator.get_directory_from_category(category)
        print(f"  {category} -> {dir_name}")


def main():
    """主测试函数"""
    print("🚀 开始测试语音脚本生成工具...")
    
    test_excel_loading()
    print("\n" + "="*50)
    
    test_directory_mapping()
    print("\n" + "="*50)
    
    test_single_generation()
    print("\n" + "="*50)
    
    print("🎉 测试完成！")


if __name__ == "__main__":
    main()
