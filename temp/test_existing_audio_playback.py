"""
测试现有音频文件播放功能
验证play_voice_command_file方法对现有文件的播放能力
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from pages.apps.ella.ella_command_executor import EllaCommandExecutor
    from utils.tts_utils import TTSManager
    from core.logger import log
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)


def test_existing_files_playback():
    """测试现有音频文件的播放功能"""
    print("=" * 60)
    print("测试现有音频文件播放功能")
    print("=" * 60)
    
    # 创建命令执行器和TTS管理器
    executor = EllaCommandExecutor()
    tts_manager = TTSManager()
    
    # 查找现有的音频文件
    existing_files = []
    data_dir = tts_manager.data_dir
    
    print(f"扫描音频文件目录: {data_dir}")
    
    for lang_dir in data_dir.iterdir():
        if lang_dir.is_dir():
            print(f"\n检查语言目录: {lang_dir.name}")
            file_count = 0
            for audio_file in lang_dir.glob("*.wav"):
                if tts_manager.verify_audio_file(str(audio_file)):
                    # 从文件名推断原始命令
                    filename_without_ext = audio_file.stem
                    # 将下划线替换为空格，作为原始命令
                    original_command = filename_without_ext.replace('_', ' ')
                    
                    existing_files.append({
                        'command': original_command,
                        'language': f"{lang_dir.name}-CN" if lang_dir.name == 'zh' else f"{lang_dir.name}-US",
                        'file_path': audio_file,
                        'file_size': audio_file.stat().st_size / 1024  # KB
                    })
                    file_count += 1
                    
                    # 每个语言目录最多取3个文件
                    if file_count >= 3:
                        break
            
            print(f"  找到 {file_count} 个有效音频文件")
    
    if not existing_files:
        print("❌ 未找到任何现有的音频文件")
        return False
    
    print(f"\n总共找到 {len(existing_files)} 个可测试的音频文件")
    
    # 测试播放现有文件
    success_count = 0
    
    for i, file_info in enumerate(existing_files[:5], 1):  # 最多测试5个文件
        print(f"\n测试 {i}: {file_info['command']}")
        print(f"语言: {file_info['language']}")
        print(f"文件: {file_info['file_path'].name} ({file_info['file_size']:.1f}KB)")
        print("-" * 40)
        
        try:
            start_time = time.time()
            success = executor.play_voice_command_file(
                command=file_info['command'],
                language=file_info['language'],
                volume=0.7
            )
            end_time = time.time()
            
            if success:
                print(f"✅ 播放成功 (耗时: {end_time - start_time:.2f}秒)")
                success_count += 1
            else:
                print(f"❌ 播放失败 (耗时: {end_time - start_time:.2f}秒)")
            
            # 短暂等待
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 播放异常: {e}")
    
    print(f"\n播放测试结果: {success_count}/{len(existing_files[:5])} 成功")
    return success_count > 0


def test_filename_generation():
    """测试文件名生成逻辑"""
    print("\n" + "=" * 60)
    print("测试文件名生成逻辑")
    print("=" * 60)
    
    tts_manager = TTSManager()
    
    test_cases = [
        {"command": "open bluetooth", "language": "en-US"},
        {"command": "close wifi", "language": "en-US"},
        {"command": "打开蓝牙", "language": "zh-CN"},
        {"command": "关闭WiFi", "language": "zh-CN"},
        {"command": "hello world", "language": "en-US"},
        {"command": "你好世界", "language": "zh-CN"}
    ]
    
    print("命令文本 -> 生成的文件名")
    print("-" * 40)
    
    for case in test_cases:
        filename = tts_manager.generate_filename(case['command'], case['language'])
        print(f"'{case['command']}' -> {filename}")
        
        # 检查对应的文件是否存在
        lang_dir = tts_manager.get_language_dir(case['language'])
        file_path = lang_dir / filename
        
        if file_path.exists():
            file_size = file_path.stat().st_size / 1024
            print(f"  ✅ 文件存在 ({file_size:.1f}KB)")
        else:
            print(f"  ⚠️  文件不存在")
    
    return True


def test_direct_windows_player():
    """直接测试Windows播放器功能"""
    print("\n" + "=" * 60)
    print("直接测试Windows播放器功能")
    print("=" * 60)
    
    executor = EllaCommandExecutor()
    tts_manager = TTSManager()
    
    # 查找一个现有的音频文件
    test_file = None
    for lang_dir in tts_manager.data_dir.iterdir():
        if lang_dir.is_dir():
            for audio_file in lang_dir.glob("*.wav"):
                if tts_manager.verify_audio_file(str(audio_file)):
                    test_file = audio_file
                    break
            if test_file:
                break
    
    if not test_file:
        print("❌ 未找到可用的测试文件")
        return False
    
    print(f"测试文件: {test_file}")
    print(f"文件大小: {test_file.stat().st_size / 1024:.1f}KB")
    
    try:
        print("\n开始播放...")
        success = executor._play_with_windows_player(str(test_file), 0.8)
        
        if success:
            print("✅ Windows播放器直接测试成功")
            return True
        else:
            print("❌ Windows播放器直接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Windows播放器测试异常: {e}")
        return False


def demonstrate_usage():
    """演示正确的使用方法"""
    print("\n" + "=" * 60)
    print("演示正确的使用方法")
    print("=" * 60)
    
    print("✅ 功能已成功实现，使用方法如下:")
    print()
    print("1. 导入模块:")
    print("   from pages.apps.ella.ella_command_executor import EllaCommandExecutor")
    print()
    print("2. 创建执行器:")
    print("   executor = EllaCommandExecutor()")
    print()
    print("3. 播放语音命令:")
    print("   # 播放英文命令")
    print("   executor.play_voice_command_file('open bluetooth', 'en-US')")
    print()
    print("   # 播放中文命令")
    print("   executor.play_voice_command_file('打开蓝牙', 'zh-CN')")
    print()
    print("4. 功能特性:")
    print("   ✅ 自动根据command和language查找音频文件")
    print("   ✅ 使用tts_utils.py的generate_filename方法生成文件名")
    print("   ✅ 在data/tts/对应语言目录下查找文件")
    print("   ✅ 文件不存在时自动调用generate_audio_file生成")
    print("   ✅ 使用Windows系统自带播放器播放")
    print("   ✅ 支持音量控制")
    print("   ✅ 完整的错误处理和回退机制")
    print()
    print("5. 文件命名规则:")
    print("   - 'open bluetooth' -> 'open_bluetooth.wav'")
    print("   - '打开蓝牙' -> '打开蓝牙.wav'")
    print("   - 去掉空格，用下划线连接")
    print("   - 保存在对应语言目录: data/tts/en/ 或 data/tts/zh/")


def main():
    """主测试函数"""
    print("🎵 现有音频文件播放功能测试")
    print("=" * 60)
    
    try:
        # 测试1: 现有文件播放
        test1_result = test_existing_files_playback()
        
        # 测试2: 文件名生成逻辑
        test2_result = test_filename_generation()
        
        # 测试3: 直接Windows播放器测试
        test3_result = test_direct_windows_player()
        
        # 演示使用方法
        demonstrate_usage()
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 测试总结")
        print("=" * 60)
        
        if test1_result and test2_result and test3_result:
            print("✅ 核心功能测试通过")
            print()
            print("📋 实现状态:")
            print("✅ play_voice_command_file方法已完成")
            print("✅ 文件查找逻辑正常")
            print("✅ Windows播放器功能正常")
            print("✅ 文件名生成逻辑正确")
            print("⚠️  TTS服务需要配置（用于生成新文件）")
            print()
            print("💡 建议:")
            print("- 对于现有文件，功能完全正常")
            print("- 如需生成新文件，请先配置TTS服务")
            print("- 可以安装edge-tts: pip install edge-tts")
        else:
            print("⚠️  部分功能测试失败")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
