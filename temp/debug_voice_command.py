"""
Ella语音指令功能调试脚本
用于测试语音指令的完整流程
"""
import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log
from pages.apps.ella.dialogue_page import EllaDialoguePage
from pages.apps.ella.ella_command_executor import EllaCommandExecutor
from testcases.test_ella.base_ella_test import SimpleEllaTest


def test_voice_command_integration():
    """测试语音指令集成功能"""
    print("=" * 60)
    print("🎤 Ella语音指令功能调试测试")
    print("=" * 60)
    
    try:
        # 1. 初始化Ella页面
        print("\n📱 步骤1: 初始化Ella页面...")
        ella_page = EllaDialoguePage()
        
        # 2. 启动应用
        print("🚀 步骤2: 启动Ella应用...")
        if not ella_page.start_app():
            print("❌ 启动应用失败")
            return False
        
        # 3. 等待页面加载
        print("⏳ 步骤3: 等待页面加载...")
        if not ella_page.wait_for_page_load():
            print("❌ 页面加载失败")
            return False
        
        # 4. 创建命令执行器
        print("🔧 步骤4: 创建命令执行器...")
        executor = EllaCommandExecutor(ella_page.driver, ella_page.page_elements)
        
        # 5. 测试文本命令（确保基础功能正常）
        print("\n📝 步骤5: 测试文本命令...")
        text_command = "hello"
        print(f"执行文本命令: {text_command}")
        text_success = executor.execute_text_command(text_command)
        print(f"文本命令结果: {'✅ 成功' if text_success else '❌ 失败'}")
        
        if text_success:
            time.sleep(3)  # 等待响应
        
        # 6. 测试语音命令
        print("\n🎤 步骤6: 测试语音命令...")
        voice_commands = [
            {"command": "open camera", "language": "en-US", "duration": 3.0},
            {"command": "打开蓝牙", "language": "zh-CN", "duration": 3.0}
        ]
        
        for i, cmd_info in enumerate(voice_commands, 1):
            print(f"\n🎵 测试语音命令 {i}/{len(voice_commands)}")
            print(f"命令: {cmd_info['command']}")
            print(f"语言: {cmd_info['language']}")
            print(f"持续时间: {cmd_info['duration']}秒")
            
            voice_success = executor.execute_voice_command(
                cmd_info['command'], 
                cmd_info['duration'], 
                cmd_info['language']
            )
            
            print(f"语音命令结果: {'✅ 成功' if voice_success else '❌ 失败'}")
            
            if voice_success:
                time.sleep(5)  # 等待响应和处理
        
        print("\n✅ 语音指令集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        log.error(f"语音指令调试测试异常: {e}")
        return False
    
    finally:
        # 清理资源
        try:
            if 'ella_page' in locals():
                print("🧹 清理资源...")
                # ella_page.close_app()  # 可选：关闭应用
        except Exception as e:
            print(f"⚠️ 清理资源时发生异常: {e}")


def test_base_ella_test_voice_methods():
    """测试BaseEllaTest类的语音方法"""
    print("\n" + "=" * 60)
    print("🧪 BaseEllaTest语音方法测试")
    print("=" * 60)
    
    try:
        # 创建测试实例
        test_instance = SimpleEllaTest()
        
        # 模拟ella_app对象
        class MockEllaApp:
            def __init__(self):
                self.driver = None
                self.page_elements = {}
            
            def execute_voice_command(self, command, duration, language):
                print(f"🎤 模拟执行语音命令: {command} (语言: {language}, 时长: {duration}秒)")
                return True
            
            def execute_text_command(self, command):
                print(f"📝 模拟执行文本命令: {command}")
                return True
            
            def wait_for_response(self, timeout=8):
                print(f"⏳ 模拟等待响应 (超时: {timeout}秒)")
                time.sleep(1)  # 模拟等待
                return True
            
            def get_response_text(self):
                return ["模拟响应文本"]
            
            def is_app_in_foreground(self):
                return True
        
        mock_app = MockEllaApp()
        
        # 测试_execute_command方法
        print("\n🔧 测试_execute_command方法...")
        
        # 测试文本命令
        print("📝 测试文本命令执行...")
        try:
            test_instance._execute_command(mock_app, "hello", is_voice=False)
            print("✅ 文本命令执行成功")
        except Exception as e:
            print(f"❌ 文本命令执行失败: {e}")
        
        # 测试语音命令
        print("🎤 测试语音命令执行...")
        try:
            test_instance._execute_command(
                mock_app, 
                "open camera", 
                is_voice=True, 
                voice_duration=3.0, 
                voice_language="en-US"
            )
            print("✅ 语音命令执行成功")
        except Exception as e:
            print(f"❌ 语音命令执行失败: {e}")
        
        print("\n✅ BaseEllaTest语音方法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ BaseEllaTest测试过程中发生异常: {e}")
        return False


def main():
    """主函数"""
    print("🎯 开始Ella语音指令功能调试")
    
    # 测试1: BaseEllaTest语音方法
    success1 = test_base_ella_test_voice_methods()
    
    # 测试2: 语音指令集成功能（需要真实设备）
    print("\n" + "=" * 60)
    print("⚠️  注意: 集成测试需要连接真实设备")
    user_input = input("是否执行集成测试? (y/N): ").strip().lower()
    
    success2 = True
    if user_input in ['y', 'yes']:
        success2 = test_voice_command_integration()
    else:
        print("⏭️  跳过集成测试")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"BaseEllaTest方法测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"集成功能测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    overall_success = success1 and success2
    print(f"\n🎯 总体结果: {'✅ 全部通过' if overall_success else '❌ 存在失败'}")
    
    return overall_success


if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        print(f"\n🏁 调试脚本执行完成 (退出码: {exit_code})")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 调试脚本执行异常: {e}")
        sys.exit(1)
