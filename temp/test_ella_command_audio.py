"""
测试Ella命令音频播放功能
验证play_voice_command_file方法的实现
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from pages.apps.ella.ella_command_executor import EllaCommandExecutor
    from core.logger import log
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)


def test_play_voice_command_file():
    """测试播放语音命令文件功能"""
    print("=" * 60)
    print("测试Ella命令音频播放功能")
    print("=" * 60)
    
    # 创建命令执行器实例
    executor = EllaCommandExecutor()
    
    # 测试用例
    test_cases = [
        {
            "command": "open bluetooth",
            "language": "en-US",
            "description": "英文命令 - 打开蓝牙"
        },
        {
            "command": "打开蓝牙",
            "language": "zh-CN", 
            "description": "中文命令 - 打开蓝牙"
        },
        {
            "command": "hello world",
            "language": "en-US",
            "description": "英文命令 - 问候语"
        },
        {
            "command": "你好世界",
            "language": "zh-CN",
            "description": "中文命令 - 问候语"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}/{total_count}: {test_case['description']}")
        print(f"命令: '{test_case['command']}'")
        print(f"语言: {test_case['language']}")
        print("-" * 40)
        
        try:
            # 执行播放测试
            start_time = time.time()
            success = executor.play_voice_command_file(
                command=test_case['command'],
                language=test_case['language'],
                volume=0.8
            )
            end_time = time.time()
            
            if success:
                print(f"✅ 测试成功 (耗时: {end_time - start_time:.2f}秒)")
                success_count += 1
            else:
                print(f"❌ 测试失败 (耗时: {end_time - start_time:.2f}秒)")
            
            # 短暂等待
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总测试用例: {total_count}")
    print(f"成功: {success_count}")
    print(f"失败: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    return success_count == total_count


def test_file_generation_and_location():
    """测试文件生成和位置验证"""
    print("\n" + "=" * 60)
    print("测试文件生成和位置验证")
    print("=" * 60)
    
    from utils.tts_utils import TTSManager
    
    # 创建TTS管理器
    tts_manager = TTSManager()
    
    # 测试命令
    test_commands = [
        {"command": "test command", "language": "en-US"},
        {"command": "测试命令", "language": "zh-CN"}
    ]
    
    for test_cmd in test_commands:
        command = test_cmd["command"]
        language = test_cmd["language"]
        
        print(f"\n测试命令: '{command}' (语言: {language})")
        
        # 1. 获取文件名
        filename = tts_manager.generate_filename(command, language)
        print(f"生成的文件名: {filename}")
        
        # 2. 获取语言目录
        lang_dir = tts_manager.get_language_dir(language)
        print(f"语言目录: {lang_dir}")
        
        # 3. 获取完整文件路径
        audio_file_path = lang_dir / filename
        print(f"音频文件路径: {audio_file_path}")
        
        # 4. 检查文件是否存在
        if audio_file_path.exists():
            file_size = audio_file_path.stat().st_size / 1024  # KB
            print(f"✅ 文件已存在 ({file_size:.1f}KB)")
            
            # 验证文件
            if tts_manager.verify_audio_file(str(audio_file_path)):
                print("✅ 文件验证通过")
            else:
                print("❌ 文件验证失败")
        else:
            print("⚠️  文件不存在，需要生成")
            
            # 尝试生成文件
            success = tts_manager.generate_audio_file(command, str(audio_file_path), language)
            if success:
                print("✅ 文件生成成功")
            else:
                print("❌ 文件生成失败")


def test_windows_player_methods():
    """测试Windows播放器方法"""
    print("\n" + "=" * 60)
    print("测试Windows播放器方法")
    print("=" * 60)
    
    # 创建命令执行器
    executor = EllaCommandExecutor()
    
    # 查找一个现有的音频文件进行测试
    from utils.tts_utils import TTSManager
    tts_manager = TTSManager()
    
    # 查找data/tts目录下的音频文件
    data_dir = tts_manager.data_dir
    audio_files = []
    
    for lang_dir in data_dir.iterdir():
        if lang_dir.is_dir():
            for audio_file in lang_dir.glob("*.wav"):
                if tts_manager.verify_audio_file(str(audio_file)):
                    audio_files.append(audio_file)
                    break  # 每个语言目录只取一个文件
    
    if not audio_files:
        print("⚠️  未找到可用的音频文件，先生成一个测试文件")
        # 生成一个测试文件
        test_command = "hello test"
        success = tts_manager.generate_audio_file(test_command, None, "en-US")
        if success:
            test_file = tts_manager.get_audio_file_path(test_command, "en-US", relative=False)
            audio_files = [Path(test_file)]
        else:
            print("❌ 无法生成测试文件")
            return False
    
    # 测试播放
    for audio_file in audio_files[:2]:  # 最多测试2个文件
        print(f"\n测试播放文件: {audio_file.name}")
        print(f"文件路径: {audio_file}")
        
        try:
            success = executor._play_with_windows_player(str(audio_file), 0.5)
            if success:
                print("✅ Windows播放器测试成功")
            else:
                print("❌ Windows播放器测试失败")
        except Exception as e:
            print(f"❌ Windows播放器测试异常: {e}")
    
    return True


def test_integration():
    """集成测试 - 完整流程"""
    print("\n" + "=" * 60)
    print("集成测试 - 完整流程")
    print("=" * 60)
    
    # 创建命令执行器
    executor = EllaCommandExecutor()
    
    # 测试一个新的命令（确保文件不存在）
    import hashlib
    import time
    
    # 生成一个唯一的测试命令
    timestamp = str(int(time.time()))
    test_command = f"integration test {timestamp}"
    
    print(f"测试命令: '{test_command}'")
    print("预期流程:")
    print("1. 检查文件是否存在（应该不存在）")
    print("2. 生成音频文件")
    print("3. 播放音频文件")
    
    try:
        success = executor.play_voice_command_file(
            command=test_command,
            language="en-US",
            volume=0.6
        )
        
        if success:
            print("✅ 集成测试成功")
            
            # 验证文件是否已生成
            from utils.tts_utils import TTSManager
            tts_manager = TTSManager()
            audio_path = tts_manager.get_audio_file_path(test_command, "en-US", relative=False)
            
            if Path(audio_path).exists():
                print(f"✅ 音频文件已生成: {audio_path}")
            else:
                print(f"⚠️  音频文件未找到: {audio_path}")
            
            return True
        else:
            print("❌ 集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🎵 Ella命令音频播放功能测试")
    print("=" * 60)
    
    try:
        # 测试1: 基本播放功能
        test1_result = test_play_voice_command_file()
        
        # 测试2: 文件生成和位置验证
        test_file_generation_and_location()
        
        # 测试3: Windows播放器方法
        test3_result = test_windows_player_methods()
        
        # 测试4: 集成测试
        test4_result = test_integration()
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 测试完成")
        print("=" * 60)
        
        if test1_result and test3_result and test4_result:
            print("✅ 所有关键测试通过")
            print("\n💡 功能说明:")
            print("- ✅ 自动根据command和language查找音频文件")
            print("- ✅ 文件不存在时自动生成")
            print("- ✅ 使用Windows系统播放器播放")
            print("- ✅ 支持多种语言")
            print("- ✅ 完整的错误处理和回退机制")
        else:
            print("⚠️  部分测试失败，请检查日志")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
