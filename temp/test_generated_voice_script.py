#!/usr/bin/env python3
"""
测试生成的语音脚本是否可以正常导入和运行
"""
import sys
import importlib.util
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def test_script_import(script_path):
    """测试脚本是否可以正常导入"""
    try:
        # 动态导入模块
        spec = importlib.util.spec_from_file_location("test_module", script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print(f"✅ 导入成功: {script_path.name}")
        
        # 检查是否有测试类
        test_classes = [attr for attr in dir(module) if attr.startswith('TestElla') and attr.endswith('Voice')]
        if test_classes:
            print(f"   📝 找到测试类: {test_classes}")
            
            # 检查测试类的属性
            for class_name in test_classes:
                test_class = getattr(module, class_name)
                if hasattr(test_class, 'command'):
                    print(f"   🎯 命令: {test_class.command}")
                if hasattr(test_class, 'voice_language'):
                    print(f"   🗣️ 语音语言: {test_class.voice_language}")
                if hasattr(test_class, 'voice_duration'):
                    print(f"   ⏱️ 语音时长: {test_class.voice_duration}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {script_path.name} - {e}")
        return False


def test_voice_scripts():
    """测试所有生成的语音脚本"""
    voice_scripts_dir = project_root / "testcases" / "test_voice_ella"
    
    if not voice_scripts_dir.exists():
        print("❌ 语音脚本目录不存在")
        return
    
    print(f"🧪 开始测试语音脚本目录: {voice_scripts_dir}")
    
    success_count = 0
    total_count = 0
    
    # 遍历所有子目录
    for subdir in voice_scripts_dir.iterdir():
        if subdir.is_dir():
            print(f"\n📁 测试目录: {subdir.name}")
            
            # 遍历目录中的Python文件
            for script_file in subdir.glob("*.py"):
                if script_file.name.startswith("test_voice_"):
                    total_count += 1
                    if test_script_import(script_file):
                        success_count += 1
    
    print(f"\n📊 测试结果:")
    print(f"   总计: {total_count} 个脚本")
    print(f"   成功: {success_count} 个脚本")
    print(f"   失败: {total_count - success_count} 个脚本")
    print(f"   成功率: {success_count/total_count*100:.1f}%" if total_count > 0 else "   成功率: 0%")


def test_specific_scripts():
    """测试特定的语音脚本"""
    test_scripts = [
        "testcases/test_voice_ella/component_coupling/test_voice_play_music.py",
        "testcases/test_voice_ella/dialogue/test_voice_global_gdp_trends.py",
        "testcases/test_voice_ella/system_coupling/test_voice_open_bluetooth.py"
    ]
    
    print("🎯 测试特定语音脚本:")
    
    for script_path_str in test_scripts:
        script_path = project_root / script_path_str
        if script_path.exists():
            print(f"\n📝 测试: {script_path_str}")
            test_script_import(script_path)
        else:
            print(f"\n❌ 文件不存在: {script_path_str}")


def main():
    """主测试函数"""
    print("🚀 开始测试生成的语音脚本...")
    
    # 测试特定脚本
    test_specific_scripts()
    
    print("\n" + "="*60)
    
    # 测试所有脚本
    test_voice_scripts()
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    main()
