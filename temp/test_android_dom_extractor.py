"""
Android DOM提取工具测试脚本
用于测试android_dom_extractor.py工具的功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from tools.android_dom_extractor import AndroidDOMExtractor
    from core.logger import log
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)


def test_basic_extraction():
    """测试基本的DOM提取功能"""
    print("=" * 60)
    print("测试基本DOM提取功能")
    print("=" * 60)
    
    # 创建提取器
    extractor = AndroidDOMExtractor()
    
    # 测试连接设备
    print("\n1. 测试设备连接...")
    if not extractor.connect_device():
        print("❌ 设备连接失败")
        return False
    print("✅ 设备连接成功")
    
    # 测试启动APP
    print("\n2. 测试启动APP...")
    package_name = "com.transsion.aivoiceassistant"
    if not extractor.launch_app(package_name):
        print(f"❌ 启动APP失败: {package_name}")
        return False
    print(f"✅ APP启动成功: {package_name}")
    
    # 等待页面加载
    print("\n3. 等待页面加载...")
    time.sleep(3)
    
    # 测试获取页面源码
    print("\n4. 测试获取页面源码...")
    xml_content = extractor.get_page_source()
    if not xml_content:
        print("❌ 获取页面源码失败")
        return False
    print(f"✅ 页面源码获取成功，长度: {len(xml_content)} 字符")
    
    # 测试保存XML文件
    print("\n5. 测试保存XML文件...")
    xml_file = extractor.save_xml_to_file(xml_content, "test_page_source.xml")
    print(f"✅ XML文件已保存: {xml_file}")
    
    # 测试解析XML元素
    print("\n6. 测试解析XML元素...")
    elements = extractor.parse_xml_elements(xml_content)
    print(f"✅ 解析完成，找到 {len(elements)} 个可点击元素")
    
    # 显示前几个元素的信息
    if elements:
        print("\n前3个可点击元素信息:")
        for i, element in enumerate(elements[:3]):
            print(f"  元素 {i+1}:")
            print(f"    类名: {element.get('class', 'N/A')}")
            print(f"    文本: {element.get('text', 'N/A')}")
            print(f"    资源ID: {element.get('resource_id', 'N/A')}")
            print(f"    坐标: {element.get('coordinates', 'N/A')}")
            print()
    
    # 测试保存JSON文件
    print("\n7. 测试保存JSON文件...")
    output_file = "test_dom_elements.json"
    if extractor.save_elements_to_json(elements, output_file):
        print(f"✅ JSON文件保存成功: tools/output/{output_file}")
    else:
        print("❌ JSON文件保存失败")
        return False
    
    return True


def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n" + "=" * 60)
    print("测试完整工作流程")
    print("=" * 60)
    
    # 创建提取器
    extractor = AndroidDOMExtractor()
    
    # 执行完整流程
    package_name = "com.transsion.aivoiceassistant"
    output_file = "test_complete_workflow.json"
    
    print(f"\n执行完整DOM提取流程...")
    print(f"目标APP: {package_name}")
    print(f"输出文件: {output_file}")
    
    success = extractor.extract_dom_elements(
        package_name=package_name,
        output_file=output_file,
        wait_time=3,
        save_xml=True
    )
    
    if success:
        print("✅ 完整工作流程测试成功")
        
        # 验证输出文件
        output_path = Path("tools/output") / output_file
        if output_path.exists():
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n输出文件验证:")
            print(f"  时间戳: {data.get('timestamp', 'N/A')}")
            print(f"  设备信息: {data.get('device_info', {}).get('brand', 'N/A')} {data.get('device_info', {}).get('model', 'N/A')}")
            print(f"  可点击元素数量: {data.get('total_clickable_elements', 0)}")
            
            return True
        else:
            print("❌ 输出文件不存在")
            return False
    else:
        print("❌ 完整工作流程测试失败")
        return False


def test_element_filtering():
    """测试元素过滤功能"""
    print("\n" + "=" * 60)
    print("测试元素过滤功能")
    print("=" * 60)
    
    # 创建提取器
    extractor = AndroidDOMExtractor()
    
    # 连接设备并获取页面源码
    if not extractor.connect_device():
        print("❌ 设备连接失败")
        return False
    
    xml_content = extractor.get_page_source()
    if not xml_content:
        print("❌ 获取页面源码失败")
        return False
    
    # 解析所有元素
    elements = extractor.parse_xml_elements(xml_content)
    print(f"总共找到 {len(elements)} 个可点击元素")
    
    # 按类型分类统计
    class_stats = {}
    text_elements = []
    button_elements = []
    
    for element in elements:
        class_name = element.get('class', 'Unknown')
        class_stats[class_name] = class_stats.get(class_name, 0) + 1
        
        if element.get('text'):
            text_elements.append(element)
        
        if 'button' in class_name.lower():
            button_elements.append(element)
    
    print(f"\n元素类型统计:")
    for class_name, count in sorted(class_stats.items()):
        print(f"  {class_name}: {count}")
    
    print(f"\n有文本的元素: {len(text_elements)}")
    print(f"按钮类元素: {len(button_elements)}")
    
    # 显示一些有文本的元素
    if text_elements:
        print(f"\n前5个有文本的元素:")
        for i, element in enumerate(text_elements[:5]):
            print(f"  {i+1}. {element.get('text', 'N/A')} ({element.get('class', 'N/A')})")
    
    return True


def main():
    """主测试函数"""
    print("Android DOM提取工具测试")
    print("=" * 60)
    
    try:
        # 测试1: 基本功能
        if not test_basic_extraction():
            print("\n❌ 基本功能测试失败")
            return
        
        # 测试2: 完整工作流程
        if not test_complete_workflow():
            print("\n❌ 完整工作流程测试失败")
            return
        
        # 测试3: 元素过滤
        if not test_element_filtering():
            print("\n❌ 元素过滤测试失败")
            return
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
