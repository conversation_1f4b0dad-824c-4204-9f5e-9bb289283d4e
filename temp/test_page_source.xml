<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][336,123]">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][236,123]">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][236,123]">

                <node index="0" text="9:15" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="9:15 PM" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][135,123]" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[135,21][236,123]">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[135,21][236,123]">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[135,21][236,123]">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android Setup notification: " checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,53][181,90]" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android System notification: " checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[190,53][227,90]" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[743,21][1035,123]">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[765,21][1035,123]">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[765,39][1035,105]">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][953,105]">

                  <node index="0" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" />

                  <node index="1" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]">

                    <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[844,41][867,102]" />

                    <node index="3" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="Phone four bars." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="Battery 100 percent." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1213]">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1213]">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1213]">

                          <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,572]">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][734,572]">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,384][734,572]">

                                <node index="0" text="Bluetooth is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,384][686,401]" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[614,455][686,527]" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,602][1032,953]">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,602][1032,953]">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,632][1032,782]">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,632][1032,782]">

                                  <node index="0" text="Bluetooth" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,674][834,735]" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,674][984,740]" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,812][1032,932]">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,845][150,899]" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,849][286,895]" />

                              </node>

                            </node>

                          </node>

                          <node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,971][1032,1079]">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,971][1032,1079]">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,971][339,1079]">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,992][156,1058]" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1007][195,1043]" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,992][297,1058]" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1109][1032,1213]">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1109][1032,1213]">

                              <node index="0" text="Dim the screen" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1109][460,1213]" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1213][1080,2400]">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1213][1080,2400]">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1213][1080,2400]">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1213][1080,2400]">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1213][1080,2400]">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1222][1080,2400]" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1222][352,1354]">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1270][352,1354]" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1354][1080,1589]">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,1393][192,1525]">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,1393][192,1525]">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,1393][181,1514]" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1413][792,1529]">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="true" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,1413][792,1529]" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1413][792,1529]" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,1423][888,1519]">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,1423][888,1519]" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,1423][1008,1519]" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="com.transsion.dynamicbar:id/island_root" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[336,22][743,121]">

    <node index="0" text="" resource-id="com.transsion.dynamicbar:id/island_group" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[336,22][743,121]">

      <node index="0" text="" resource-id="com.transsion.dynamicbar:id/contentView" class="android.view.ViewGroup" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[336,22][743,121]">

        <node index="0" text="" resource-id="com.transsion.dynamicbar:id/startView" class="androidx.appcompat.widget.LinearLayoutCompat" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[336,36][456,108]">

          <node index="0" text="" resource-id="com.transsion.dynamicbar:id/startViewOne" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[336,36][456,108]">

            <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[336,36][456,108]">

              <node index="0" text="" resource-id="com.transsion.dynamicbar:id/itemContent" class="android.widget.ImageView" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[357,36][429,108]" />

            </node>

          </node>

        </node>

        <node index="1" text="" resource-id="com.transsion.dynamicbar:id/endView" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[672,22][743,121]">

          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[672,22][743,121]">

            <node index="0" text="1" resource-id="com.transsion.dynamicbar:id/itemContent" class="android.widget.TextView" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[693,41][713,102]" />

          </node>

        </node>

      </node>

      <node index="1" text="" resource-id="com.transsion.dynamicbar:id/subIslandView" class="android.widget.FrameLayout" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[410,32][668,110]" />

      <node index="2" text="" resource-id="com.transsion.dynamicbar:id/animalView" class="android.view.View" package="com.transsion.dynamicbar" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[538,70][541,73]" />

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2400]">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

      <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

        <node index="0" text="" resource-id="android:id/parentPanel" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

          <node index="0" text="" resource-id="android:id/inputArea" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

            <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/keyboard_holder" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/keyboard_header_view_holder" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,1710]">

                    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,1710]">

                      <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.view.ViewGroup" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,1710]">

                        <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.view.View" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,1589][1074,1710]" />

                        <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_header_access_points_menu" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Open features menu" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,1589][122,1710]">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[17,1602][111,1696]">

                            <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[31,1616][97,1682]" />

                          </node>

                        </node>

                        <node index="2" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[122,1589][958,1710]">

                          <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[122,1589][958,1710]">

                            <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.view.ViewGroup" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[122,1589][958,1710]">

                              <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Sticker Keyboard" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[145,1589][303,1710]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[153,1605][295,1693]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[193,1618][254,1679]" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="GIF Keyboard" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[303,1589][461,1710]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[311,1605][453,1693]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[351,1618][412,1679]" />

                                </node>

                              </node>

                              <node index="2" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Clipboard" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[461,1589][619,1710]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[469,1605][611,1693]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[509,1618][570,1679]" />

                                </node>

                              </node>

                              <node index="3" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Settings" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[619,1589][777,1710]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[627,1605][769,1693]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[667,1618][728,1679]" />

                                </node>

                              </node>

                              <node index="4" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Theme settings" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[777,1589][935,1710]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[785,1605][927,1693]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[825,1618][886,1679]" />

                                </node>

                              </node>

                            </node>

                          </node>

                        </node>

                        <node index="3" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_header_power_key" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Use voice typing" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[958,1589][1074,1710]">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[958,1589][1074,1710]">

                            <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[969,1602][1063,1696]" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1710][1080,2290]">

                    <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1710][1080,2290]">

                      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1710][1080,2290]">

                        <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1710][1080,2290]">

                          <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/input_area" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,1732][1074,2148]">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,1732][1074,1870]">

                              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_0" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="q" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[6,1732][112,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[12,1732][106,1843]" />

                                <node index="1" text="1" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1732][106,1774]" />

                              </node>

                              <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_1" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="w" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[112,1732][218,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[118,1732][212,1843]" />

                                <node index="1" text="2" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[191,1732][212,1774]" />

                              </node>

                              <node index="2" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_2" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="e" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[218,1732][325,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[224,1732][319,1843]" />

                                <node index="1" text="3" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[298,1732][319,1774]" />

                              </node>

                              <node index="3" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_3" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="r" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[325,1732][432,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[331,1732][426,1843]" />

                                <node index="1" text="4" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[404,1732][426,1774]" />

                              </node>

                              <node index="4" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_4" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="t" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[432,1732][539,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[438,1732][533,1843]" />

                                <node index="1" text="5" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[511,1732][533,1774]" />

                              </node>

                              <node index="5" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_5" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="y" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[539,1732][646,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[545,1732][640,1843]" />

                                <node index="1" text="6" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[618,1732][640,1774]" />

                              </node>

                              <node index="6" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_6" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="u" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[646,1732][753,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[652,1732][747,1843]" />

                                <node index="1" text="7" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[727,1732][747,1774]" />

                              </node>

                              <node index="7" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_7" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="i" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[753,1732][860,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[759,1732][854,1843]" />

                                <node index="1" text="8" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[833,1732][854,1774]" />

                              </node>

                              <node index="8" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_8" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="o" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[860,1732][967,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[866,1732][961,1843]" />

                                <node index="1" text="9" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[939,1732][961,1774]" />

                              </node>

                              <node index="9" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_0_9" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="p" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[967,1732][1074,1870]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[973,1732][1068,1843]" />

                                <node index="1" text="0" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1043,1732][1068,1774]" />

                              </node>

                            </node>

                            <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,1870][1074,2009]">

                              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_0" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="a" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[6,1870][166,2009]">

                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,1870][166,2009]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/host" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,1870][166,2009]">

                                    <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[66,1870][160,1982]" />

                                  </node>

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_1" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="s" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[166,1870][272,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[172,1870][266,1982]" />

                              </node>

                              <node index="2" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_2" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="d" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[272,1870][378,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[278,1870][372,1982]" />

                              </node>

                              <node index="3" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_3" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="f" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[378,1870][485,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[384,1870][479,1982]" />

                              </node>

                              <node index="4" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_4" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="g" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[485,1870][592,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[491,1870][586,1982]" />

                              </node>

                              <node index="5" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_5" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="h" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[592,1870][699,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[598,1870][693,1982]" />

                              </node>

                              <node index="6" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_6" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="j" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[699,1870][806,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[705,1870][800,1982]" />

                              </node>

                              <node index="7" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_7" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="k" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[806,1870][913,2009]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[812,1870][907,1982]" />

                              </node>

                              <node index="8" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_1_8" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="l" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[913,1870][1074,2009]">

                                <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[913,1870][1074,2009]">

                                  <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/host" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[913,1870][1020,2009]">

                                    <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[919,1870][1014,1982]" />

                                  </node>

                                </node>

                              </node>

                            </node>

                            <node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,2009][1074,2148]">

                              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_shift" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Shift" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[6,2009][166,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[47,2026][124,2103]" />

                              </node>

                              <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_1" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="z" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,2009][272,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[172,2009][266,2121]" />

                              </node>

                              <node index="2" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_2" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="x" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[272,2009][378,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[278,2009][372,2121]" />

                              </node>

                              <node index="3" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_3" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="c" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[378,2009][485,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[384,2009][479,2121]" />

                              </node>

                              <node index="4" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_4" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="v" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[485,2009][592,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[491,2009][586,2121]" />

                              </node>

                              <node index="5" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_5" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="b" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[592,2009][699,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[598,2009][693,2121]" />

                              </node>

                              <node index="6" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_6" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="n" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[699,2009][806,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[705,2009][800,2121]" />

                              </node>

                              <node index="7" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_2_7" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="m" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[806,2009][913,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[812,2009][907,2121]" />

                              </node>

                              <node index="8" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_del" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Delete" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[913,2009][1074,2148]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[955,2026][1032,2103]" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,2148][1074,2290]">

                            <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_switch_to_symbol" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Symbol keyboard" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[6,2148][166,2290]">

                              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[46,2179][125,2231]" />

                            </node>

                            <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_bottom_symbol_1" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="," checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[166,2148][272,2290]">

                              <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[172,2148][266,2263]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[201,2148][237,2217]" />

                              </node>

                              <node index="1" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[214,2168][224,2246]" />

                            </node>

                            <node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[272,2148][806,2290]">

                              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_switch_to_next_language" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Next language" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[272,2148][378,2290]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[292,2172][358,2238]" />

                              </node>

                              <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[378,2148][806,2290]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_space" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Space" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[378,2148][806,2290]">

                                  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[384,2148][800,2263]">

                                    <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[384,2148][800,2263]" />

                                  </node>

                                </node>

                              </node>

                            </node>

                            <node index="3" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_bottom_symbol_2" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="." checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[806,2148][913,2290]">

                              <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[855,2168][863,2246]" />

                            </node>

                            <node index="4" text="" resource-id="com.google.android.inputmethod.latin:id/key_pos_ime_action" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="Done" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[913,2148][1074,2290]">

                              <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[919,2148][1068,2263]">

                                <node index="0" text="" resource-id="com.google.android.inputmethod.latin:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[960,2172][1026,2238]" />

                                <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[987,2199][1000,2212]" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

                <node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,2290]">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1589][1080,1592]" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="1" text="" resource-id="android:id/navigationBarBackground" class="android.view.View" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2290][1080,2400]" />

    <node index="2" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2290][1080,2400]">

      <node index="0" text="" resource-id="android:id/input_method_navigation_bar_view" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2290][1080,2400]">

        <node index="0" text="" resource-id="android:id/input_method_nav_inflater" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2290][1080,2400]">

          <node index="0" text="" resource-id="android:id/input_method_nav_horizontal" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2290][1080,2400]">

            <node index="0" text="" resource-id="android:id/input_method_nav_buttons" class="android.widget.FrameLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[22,2290][1058,2400]">

              <node index="0" text="" resource-id="android:id/input_method_nav_ends_group" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[22,2290][1058,2400]">

                <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[22,2290][214,2400]">

                  <node index="0" text="" resource-id="android:id/input_method_nav_back" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="Back" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[22,2290][214,2400]" />

                </node>

                <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[866,2290][1058,2400]">

                  <node index="0" text="" resource-id="android:id/input_method_nav_ime_switcher" class="android.widget.ImageView" package="com.google.android.inputmethod.latin" content-desc="Switch input method" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[866,2290][1058,2400]" />

                </node>

              </node>

              <node index="1" text="" resource-id="android:id/input_method_nav_center_group" class="android.widget.LinearLayout" package="com.google.android.inputmethod.latin" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[441,2290][639,2400]" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

</hierarchy>