#!/usr/bin/env python3
"""
语音按钮优化测试脚本
测试优化后的语音按钮点击逻辑
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


class VoiceButtonOptimizationTester:
    """语音按钮优化测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.ella_page = None
        self.test_results = []
    
    def setup(self) -> bool:
        """设置测试环境"""
        try:
            log.info("🔧 设置测试环境...")
            
            # 初始化Ella页面
            self.ella_page = EllaDialoguePage()
            
            # 启动应用
            if not self.ella_page.start_app():
                log.error("❌ 启动Ella应用失败")
                return False
            
            # 等待页面加载
            if not self.ella_page.wait_for_page_load(timeout=15):
                log.error("❌ 等待页面加载失败")
                return False
            
            log.info("✅ 测试环境设置完成")
            return True
            
        except Exception as e:
            log.error(f"设置测试环境失败: {e}")
            return False
    
    def test_voice_button_detection(self) -> bool:
        """测试语音按钮检测"""
        try:
            log.info("🔍 测试语音按钮检测...")
            
            # 检查主要语音按钮
            voice_button = self.ella_page.voice_input_button
            if voice_button.is_exists():
                log.info("✅ 主要语音按钮检测成功")
                self.test_results.append("主要语音按钮: 存在")
            else:
                log.warning("⚠️ 主要语音按钮不存在")
                self.test_results.append("主要语音按钮: 不存在")
            
            # 检查备选语音按钮
            voice_button_alt = self.ella_page.voice_button_alt
            if voice_button_alt.is_exists():
                log.info("✅ 备选语音按钮检测成功")
                self.test_results.append("备选语音按钮: 存在")
            else:
                log.warning("⚠️ 备选语音按钮不存在")
                self.test_results.append("备选语音按钮: 不存在")
            
            return True
            
        except Exception as e:
            log.error(f"语音按钮检测测试失败: {e}")
            self.test_results.append(f"语音按钮检测: 失败 - {e}")
            return False
    
    def test_voice_input_start_strategies(self) -> bool:
        """测试语音输入启动策略"""
        try:
            log.info("🎤 测试语音输入启动策略...")
            
            # 获取命令执行器
            executor = self.ella_page.command_executor
            
            # 测试启动语音输入
            if executor._start_voice_input():
                log.info("✅ 语音输入启动成功")
                self.test_results.append("语音输入启动: 成功")
                
                # 等待一段时间
                time.sleep(2)
                
                # 测试停止语音输入
                if executor._stop_voice_input():
                    log.info("✅ 语音输入停止成功")
                    self.test_results.append("语音输入停止: 成功")
                else:
                    log.warning("⚠️ 语音输入停止失败")
                    self.test_results.append("语音输入停止: 失败")
                
                return True
            else:
                log.error("❌ 语音输入启动失败")
                self.test_results.append("语音输入启动: 失败")
                return False
            
        except Exception as e:
            log.error(f"语音输入启动策略测试失败: {e}")
            self.test_results.append(f"语音输入启动策略: 失败 - {e}")
            return False
    
    def test_voice_command_execution(self) -> bool:
        """测试语音命令执行"""
        try:
            log.info("🗣️ 测试语音命令执行...")
            
            # 测试简单的语音命令
            test_command = "hello"
            
            if self.ella_page.execute_voice_command(test_command, duration=2.0):
                log.info("✅ 语音命令执行成功")
                self.test_results.append(f"语音命令执行 ('{test_command}'): 成功")
                
                # 等待响应
                time.sleep(3)
                
                # 获取响应
                response = self.ella_page.get_response_text()
                if response:
                    log.info(f"✅ 获取到响应: {response[:100]}...")
                    self.test_results.append(f"语音命令响应: 有响应")
                else:
                    log.warning("⚠️ 未获取到响应")
                    self.test_results.append("语音命令响应: 无响应")
                
                return True
            else:
                log.error("❌ 语音命令执行失败")
                self.test_results.append(f"语音命令执行 ('{test_command}'): 失败")
                return False
            
        except Exception as e:
            log.error(f"语音命令执行测试失败: {e}")
            self.test_results.append(f"语音命令执行: 失败 - {e}")
            return False
    
    def test_fallback_mechanism(self) -> bool:
        """测试回退机制"""
        try:
            log.info("🔄 测试回退机制...")
            
            # 模拟语音输入失败的情况，测试是否能回退到文本输入
            test_command = "open bluetooth"
            
            # 直接调用命令执行器的语音命令方法
            executor = self.ella_page.command_executor
            
            if executor.execute_voice_command(test_command, duration=1.0):
                log.info("✅ 语音命令（含回退机制）执行成功")
                self.test_results.append("回退机制测试: 成功")
                
                # 等待响应
                time.sleep(3)
                
                # 检查响应
                response = self.ella_page.get_response_text()
                if response and "bluetooth" in response.lower():
                    log.info("✅ 回退机制响应正确")
                    self.test_results.append("回退机制响应: 正确")
                else:
                    log.warning("⚠️ 回退机制响应不明确")
                    self.test_results.append("回退机制响应: 不明确")
                
                return True
            else:
                log.error("❌ 语音命令（含回退机制）执行失败")
                self.test_results.append("回退机制测试: 失败")
                return False
            
        except Exception as e:
            log.error(f"回退机制测试失败: {e}")
            self.test_results.append(f"回退机制测试: 失败 - {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        try:
            log.info("🚀 开始语音按钮优化测试...")
            
            # 设置测试环境
            if not self.setup():
                return False
            
            # 运行各项测试
            tests = [
                ("语音按钮检测", self.test_voice_button_detection),
                ("语音输入启动策略", self.test_voice_input_start_strategies),
                ("语音命令执行", self.test_voice_command_execution),
                ("回退机制", self.test_fallback_mechanism),
            ]
            
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_func in tests:
                log.info(f"\n{'='*50}")
                log.info(f"🧪 执行测试: {test_name}")
                log.info(f"{'='*50}")
                
                try:
                    if test_func():
                        log.info(f"✅ {test_name} - 通过")
                        passed_tests += 1
                    else:
                        log.error(f"❌ {test_name} - 失败")
                except Exception as e:
                    log.error(f"❌ {test_name} - 异常: {e}")
                
                # 测试间隔
                time.sleep(2)
            
            # 输出测试结果
            self.print_test_summary(passed_tests, total_tests)
            
            return passed_tests == total_tests
            
        except Exception as e:
            log.error(f"运行测试失败: {e}")
            return False
    
    def print_test_summary(self, passed: int, total: int):
        """打印测试摘要"""
        log.info(f"\n{'='*60}")
        log.info(f"📊 语音按钮优化测试结果摘要")
        log.info(f"{'='*60}")
        log.info(f"总测试数: {total}")
        log.info(f"通过测试: {passed}")
        log.info(f"失败测试: {total - passed}")
        log.info(f"通过率: {(passed/total)*100:.1f}%")
        
        log.info(f"\n📋 详细结果:")
        for i, result in enumerate(self.test_results, 1):
            log.info(f"{i:2d}. {result}")
        
        if passed == total:
            log.info(f"\n🎉 所有测试通过！语音按钮优化成功！")
        else:
            log.info(f"\n⚠️ 部分测试失败，需要进一步优化")
        
        log.info(f"{'='*60}")


def main():
    """主函数"""
    try:
        log.info("🎯 语音按钮优化测试开始")
        
        # 创建测试器
        tester = VoiceButtonOptimizationTester()
        
        # 运行测试
        success = tester.run_all_tests()
        
        if success:
            log.info("🎉 语音按钮优化测试完成 - 全部通过")
            return 0
        else:
            log.error("❌ 语音按钮优化测试完成 - 部分失败")
            return 1
            
    except Exception as e:
        log.error(f"测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
