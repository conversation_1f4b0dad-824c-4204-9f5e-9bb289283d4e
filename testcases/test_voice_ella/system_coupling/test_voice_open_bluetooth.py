"""
Ella语音助手语音指令测试
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenBluetoothVoice(SimpleEllaTest):
    """Ella打开bluetooth测试类"""

    @allure.title("测试open bluetooth")
    @allure.description("测试open bluetooth指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_bluetooth_voice(self, ella_app):
        """测试open bluetooth命令"""
        command = "open bluetooth"
        app_name = 'bluetooth'

        with allure.step(f"执行语音命令: {command} (语言: {voice_language})"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command,
                is_voice=True,  # 语音标识为True
                voice_duration=voice_duration,
                voice_language=voice_language
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = ['Bluetooth is turned on now']
    voice_language = "en-US"  # 语音语言
    voice_duration = 3.0      # 语音持续时间
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加语音相关信息
            voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒"""
            summary += voice_info
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
