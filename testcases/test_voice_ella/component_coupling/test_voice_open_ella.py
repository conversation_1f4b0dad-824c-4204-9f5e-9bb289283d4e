"""
Ella语音助手语音指令测试
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开")
class TestEllaCommandConciseVoice(SimpleEllaTest):
    """Ella联系人命令测试类"""
    command = "open ella"
    expected_text = ["YouTube","Instagram","Visha","which app should i open"]
    voice_language = "en-US"  # 语音语言
    voice_duration = 3.0      # 语音持续时间

    @allure.title("测试open contact命令 - 简洁版本")
    @allure.description("使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_ella_voice(self, ella_app):
        """测试open contact命令 - 简洁版本"""
        command = "open ella"

        app_name = 'ella'

        with allure.step(f"执行语音命令: {command} (语言: {voice_language})"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command,
                is_voice=True,  # 语音标识为True
                voice_duration=voice_duration,
                voice_language=voice_language
            )

        with allure.step("验证响应包含Done"):
            # expected_text =["YouTube","Instagram","Visha","which app should i open"]
            expected_text =["Done"]
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含'Done'，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加语音相关信息
            voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒"""
            summary += voice_info
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
