"""
Ella语音助手语音指令测试
"""
import pytest
import allure
import logging
from testcases.test_ella.base_ella_test import SimpleEllaTest

log = logging.getLogger(__name__)


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaWhatSWeatherLikeShanghaiTodayVoice(SimpleEllaTest):
    """Ella What's the weather like in Shanghai today 测试类"""
    command = "What's the weather like in Shanghai today"
    expected_text = ["Done"]  # 根据实际需要调整
    voice_language = "en-US"  # 语音语言
    voice_duration = 3.0      # 语音持续时间
    command = "What's the weather like in Shanghai today"
    expected_text = ['today', 'Pudong']

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_what_s_the_weather_like_in_shanghai_today_voice(self, ella_app):
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行语音命令: {command} (语言: {voice_language})"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"
            
        with allure.step("验证天气温度在有效范围内(-10℃ ~ 50℃)"):
            import re
            # 确保response_text是字符串类型
            if isinstance(response_text, list):
                # 如果是列表，将其合并为一个字符串
                response_str = " ".join([str(text) for text in response_text if text])
            else:
                # 如果已经是字符串，直接使用
                response_str = str(response_text)
                
            # 从响应文本中提取温度值（包括负数）
            temp_pattern = r"(-?\d+)\s*℃"
            temp_match = re.search(temp_pattern, response_str)
            
            if temp_match:
                temperature = int(temp_match.group(1))
                assert -10 <= temperature <= 50, f"温度应在-10℃到50℃范围内，实际温度: {temperature}℃"
                allure.attach(f"温度值: {temperature}℃", "温度验证", allure.attachment_type.TEXT)
            else:
                # 如果没有找到温度值，记录但不中断测试
                allure.attach(f"无法从响应中提取温度值: '{response_str}'", "温度验证失败", allure.attachment_type.TEXT)
                log.warning(f"⚠️ 无法从响应中提取温度值，响应文本: '{response_str}'")
                # 不使用断言失败，避免中断测试

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加语音相关信息
            voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒"""
            summary += voice_info
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
