"""
Ella语音助手中文语音指令测试
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("Ella语音助手中文语音指令")
class TestEllaVoiceCommandChinese(SimpleEllaTest):
    """Ella中文语音指令测试类"""
    command = "打开蓝牙"
    expected_text = ["Done", "完成", "已打开"]
    voice_language = "zh-CN"  # 中文语音
    voice_duration = 3.0      # 语音持续时间

    @allure.title(f"测试中文语音指令: {command}")
    @allure.description(f"使用中文语音执行命令: {command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_voice_open_bluetooth(self, ella_app):
        f"""中文语音指令测试: {self.command}"""

        command = self.command
        voice_language = self.voice_language
        voice_duration = self.voice_duration

        with allure.step(f"执行中文语音命令: {command} (语言: {voice_language})"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, 
                command, 
                verify_status=False,  # 蓝牙设置通常不验证状态变化
                is_voice=True,        # 语音标识为True
                voice_duration=voice_duration,
                voice_language=voice_language
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)
            assert result, f"响应文本应包含{expected_text}中的任意一个，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加语音相关信息
            voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒
命令类型: 中文语音指令"""
            summary += voice_info
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "chinese_voice_test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
