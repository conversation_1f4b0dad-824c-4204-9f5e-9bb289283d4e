# Android DOM元素提取工具使用说明

## 概述

`android_dom_extractor.py` 是一个专业的Android应用DOM元素提取工具，能够自动获取Android手机当前页面的DOM元素，提取所有可点击的元素信息，并以JSON格式输出。

## 功能特性

- ✅ **自动连接设备**: 支持USB连接和WiFi连接的Android设备
- ✅ **应用启动**: 自动启动指定包名的Android应用
- ✅ **DOM树获取**: 获取当前页面完整的UI层次结构
- ✅ **XML文件保存**: 将原始XML DOM树保存到本地文件
- ✅ **智能解析**: 解析XML并提取所有可点击元素的详细信息
- ✅ **JSON输出**: 以结构化JSON格式输出元素信息
- ✅ **坐标计算**: 自动计算元素的中心点坐标和尺寸信息
- ✅ **多设备支持**: 支持指定特定设备ID进行操作

## 安装依赖

确保已安装必要的Python包：

```bash
pip install uiautomator2
```

## 使用方法

### 基本用法

```bash
# 提取指定APP的可点击元素
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant
```

### 高级用法

```bash
# 指定输出文件名
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --output ella_elements.json

# 指定设备ID（多设备环境）
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --device emulator-5554

# 设置启动等待时间
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --wait-time 5

# 不保存XML文件
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --no-save-xml

# 等待页面加载完成
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --wait-page-load
```

## 命令行参数

| 参数 | 简写 | 必需 | 说明 | 默认值 |
|------|------|------|------|--------|
| `--package` | `-p` | ✅ | APP包名 | 无 |
| `--output` | `-o` | ❌ | 输出JSON文件名 | 自动生成 |
| `--device` | `-d` | ❌ | 设备ID | 第一个设备 |
| `--wait-time` | `-w` | ❌ | 启动APP后等待时间（秒） | 3 |
| `--no-save-xml` | 无 | ❌ | 不保存XML文件 | False |
| `--wait-page-load` | 无 | ❌ | 等待页面加载完成 | False |

## 输出文件结构

### JSON输出格式

```json
{
  "timestamp": "2025-08-27T14:30:00.123456",
  "device_info": {
    "brand": "TECNO",
    "model": "SPARK 20",
    "serial": "ABC123456",
    "version": "13"
  },
  "total_clickable_elements": 15,
  "elements": [
    {
      "xpath": "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/...",
      "class": "android.widget.Button",
      "resource_id": "com.transsion.aivoiceassistant:id/btn_start",
      "text": "开始对话",
      "content_desc": "开始语音对话按钮",
      "bounds": "[100,200][300,250]",
      "clickable": "true",
      "focusable": "true",
      "long_clickable": "false",
      "enabled": "true",
      "selected": "false",
      "checked": "false",
      "package": "com.transsion.aivoiceassistant",
      "index": "0",
      "scrollable": "false",
      "coordinates": {
        "left": 100,
        "top": 200,
        "right": 300,
        "bottom": 250,
        "center_x": 200,
        "center_y": 225,
        "width": 200,
        "height": 50
      }
    }
  ]
}
```

### 元素属性说明

| 属性 | 说明 | 示例 |
|------|------|------|
| `xpath` | 元素的XPath路径 | `/hierarchy/android.widget.FrameLayout/...` |
| `class` | 元素的类名 | `android.widget.Button` |
| `resource_id` | 资源ID | `com.example:id/button1` |
| `text` | 显示文本 | `确定` |
| `content_desc` | 内容描述（无障碍） | `确定按钮` |
| `bounds` | 边界坐标 | `[0,0][100,50]` |
| `clickable` | 是否可点击 | `true`/`false` |
| `focusable` | 是否可获得焦点 | `true`/`false` |
| `long_clickable` | 是否支持长按 | `true`/`false` |
| `enabled` | 是否启用 | `true`/`false` |
| `selected` | 是否选中 | `true`/`false` |
| `checked` | 是否勾选 | `true`/`false` |
| `package` | 所属包名 | `com.example.app` |
| `index` | 在父容器中的索引 | `0` |
| `scrollable` | 是否可滚动 | `true`/`false` |
| `coordinates` | 计算后的坐标信息 | 见上表 |

## 文件输出位置

- **JSON文件**: `tools/output/` 目录
- **XML文件**: `temp/` 目录

## 使用示例

### 示例1: 提取Ella语音助手的可点击元素

```bash
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --output ella_ui_elements.json
```

### 示例2: 在多设备环境中指定设备

```bash
# 先查看连接的设备
adb devices

# 指定设备提取
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --device emulator-5554
```

### 示例3: 调试模式（保存XML文件）

```bash
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant --wait-time 5 --wait-page-load
```

## 测试工具

项目提供了专门的测试脚本来验证工具功能：

```bash
python temp/test_android_dom_extractor.py
```

测试脚本包含：
- 基本功能测试
- 完整工作流程测试  
- 元素过滤功能测试

## 常见问题

### Q1: 设备连接失败
**A**: 确保：
- 设备已开启USB调试
- 已安装ADB驱动
- 设备已授权计算机调试
- UIAutomator2服务正常运行

### Q2: APP启动失败
**A**: 检查：
- APP包名是否正确
- APP是否已安装在设备上
- 设备存储空间是否充足

### Q3: 获取不到页面元素
**A**: 尝试：
- 增加等待时间 `--wait-time`
- 使用 `--wait-page-load` 等待页面加载
- 检查页面是否完全加载

### Q4: XML解析失败
**A**: 可能原因：
- 页面包含特殊字符
- UIAutomator2服务异常
- 设备性能问题导致XML不完整

## 技术实现

### 核心技术栈
- **UIAutomator2**: Android UI自动化框架
- **ADB**: Android调试桥
- **XML解析**: Python内置xml.etree.ElementTree
- **JSON处理**: Python内置json模块

### 工作流程
1. 连接Android设备并检查UIAutomator2服务
2. 启动指定包名的Android应用
3. 等待应用完全加载
4. 获取当前页面的XML DOM树
5. 解析XML并提取可点击元素信息
6. 计算元素坐标和尺寸信息
7. 生成结构化JSON输出文件

## 扩展功能

工具支持以下扩展：
- 自定义元素过滤条件
- 批量处理多个应用
- 页面截图与元素标注
- 元素交互测试

## 注意事项

1. **设备要求**: Android 4.4+ (API 19+)
2. **权限要求**: 需要USB调试权限
3. **性能影响**: 大型应用的DOM树可能较大，解析时间较长
4. **网络要求**: 首次使用需要下载UIAutomator2 APK文件

## 工具套件

本项目提供了完整的Android DOM元素提取工具套件：

### 1. 基础工具
- **`android_dom_extractor.py`**: 核心DOM提取工具
- **`quick_dom_extract.py`**: 快速提取工具（专门针对Ella）
- **`run_dom_extract.bat`**: Windows批处理启动脚本

### 2. 高级工具
- **`advanced_dom_extractor.py`**: 高级DOM提取工具（支持截图、交互测试）
- **`dom_extract_config.json`**: 配置文件模板

### 3. 测试和示例
- **`temp/test_android_dom_extractor.py`**: 功能测试脚本
- **`tools/examples/dom_extractor_examples.py`**: 使用示例集合

## 快速开始

### 方法1: 使用快速工具（推荐）
```bash
# 直接运行快速提取工具
python tools/quick_dom_extract.py

# 或使用批处理文件（Windows）
tools/run_dom_extract.bat
```

### 方法2: 使用完整工具
```bash
# 基础提取
python tools/android_dom_extractor.py --package com.transsion.aivoiceassistant

# 高级提取（带截图）
python tools/advanced_dom_extractor.py --package com.transsion.aivoiceassistant --screenshot
```

### 方法3: 运行示例
```bash
# 查看各种使用示例
python tools/examples/dom_extractor_examples.py
```

## 工具对比

| 功能 | 基础工具 | 快速工具 | 高级工具 |
|------|----------|----------|----------|
| DOM提取 | ✅ | ✅ | ✅ |
| JSON输出 | ✅ | ✅ | ✅ |
| XML保存 | ✅ | ✅ | ✅ |
| 元素分类 | ❌ | ❌ | ✅ |
| 截图标注 | ❌ | ❌ | ✅ |
| 交互测试 | ❌ | ❌ | ✅ |
| 关键词过滤 | ❌ | ❌ | ✅ |
| 批量处理 | ❌ | ❌ | ✅ |
| 配置文件 | ❌ | ❌ | ✅ |

## 更新日志

### v1.0.0 (2025-08-27)
- 初始版本发布
- 支持基本的DOM元素提取功能
- 支持JSON格式输出
- 支持多设备环境
- 提供快速提取工具
- 提供高级分析功能
- 包含完整的测试和示例
