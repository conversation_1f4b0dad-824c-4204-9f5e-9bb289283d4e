# 语音脚本生成工具使用指南

## 概述

语音脚本生成工具 (`voice_script_generator.py`) 是一个自动化工具，用于根据Excel数据和现有文本脚本生成语音测试脚本。该工具基于 `test_voice_open_camera.py` 的实现方式，自动生成标准化的语音测试用例。

## 功能特性

### 1. 自动数据读取
- 读取 `data/query/query_v3.xlsx` 文件
- 提取 `query` 和 `类别` 字段
- 支持75条测试数据的批量处理

### 2. 智能目录映射
根据类别字段自动确定输出目录：
- `模块耦合` → `component_coupling`
- `dialogue` → `dialogue`
- `系统耦合` → `system_coupling`
- `三方耦合` → `third_coupling`
- `不支持指令` → `unsupported_commands`
- `自身功能` → `self_function`

### 3. 模板匹配
- 在 `testcases/test_ella` 对应目录下查找文本脚本作为模板
- 支持精确匹配和模糊匹配
- 基于关键词匹配算法提高匹配成功率

### 4. 语音脚本生成
- 自动转换文本脚本为语音脚本
- 智能提取原始脚本的 `expected_text` 值，确保期望响应保持一致
- 添加语音相关参数和配置
- 默认语音设置：`voice_language = "en-US"`，`voice_duration = 3.0`

## 使用方法

### 基本使用

```bash
# 直接运行工具，处理所有Excel数据
python tools/voice_script_generator.py
```

### 编程方式使用

```python
from tools.voice_script_generator import VoiceScriptGenerator

# 创建生成器实例
generator = VoiceScriptGenerator()

# 生成单个语音脚本
file_path = generator.generate_voice_script("play music", "模块耦合")

# 生成所有语音脚本
generated_files = generator.generate_all_voice_scripts()
```

## 输出结构

生成的语音脚本保存在 `testcases/test_voice_ella` 目录下：

```
testcases/test_voice_ella/
├── component_coupling/
│   ├── test_voice_play_music.py
│   ├── test_voice_open_contact.py
│   └── ...
├── dialogue/
│   ├── test_voice_global_gdp_trends.py
│   ├── test_voice_hi.py
│   └── ...
├── system_coupling/
│   ├── test_voice_open_bluetooth.py
│   └── ...
└── ...
```

## 生成的脚本特性

### 1. 语音参数配置
```python
class TestEllaOpenVishaVoice(SimpleEllaTest):
    command = "play music"
    expected_text = ["Done"]  # 自动从原始文本脚本提取
    voice_language = "en-US"  # 语音语言
    voice_duration = 3.0      # 语音持续时间
```

### 2. 语音测试方法
```python
def test_play_music_voice(self, ella_app):
    """测试play music命令"""
    command = "play music"
    voice_language = self.voice_language
    voice_duration = self.voice_duration
    
    with allure.step(f"执行语音命令: {command} (语言: {voice_language})"):
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, command,
            is_voice=True,  # 语音标识为True
            voice_duration=voice_duration,
            voice_language=voice_language
        )
```

### 3. 语音信息记录
```python
# 添加语音相关信息
voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒"""
summary += voice_info
```

## 配置说明

### 默认配置
- **Excel文件路径**: `data/query/query_v3.xlsx`
- **文本脚本目录**: `testcases/test_ella`
- **语音脚本输出目录**: `testcases/test_voice_ella`
- **默认语音语言**: `en-US`
- **默认语音持续时间**: `3.0` 秒

### 自定义配置
可以通过修改 `VoiceScriptGenerator` 类的属性来自定义配置：

```python
generator = VoiceScriptGenerator()
generator.default_voice_language = "zh-CN"  # 修改默认语音语言
generator.default_voice_duration = 5.0      # 修改默认持续时间
```

## 运行结果示例

```
🚀 开始生成语音脚本...
✅ play music -> 模块耦合 -> D:\aigc\app_test\testcases\test_voice_ella\component_coupling\test_voice_play_music.py
✅ global gdp trends -> dialogue -> D:\aigc\app_test\testcases\test_voice_ella\dialogue\test_voice_global_gdp_trends.py
❌ 跳过: set an alarm at 8 am (未找到对应文本脚本)

🎉 语音脚本生成完成！
📊 总共生成 47 个语音脚本
📁 输出目录: D:\aigc\app_test\testcases\test_voice_ella
```

## 注意事项

### 1. 依赖要求
- pandas：用于Excel文件读取
- 现有的文本脚本作为模板
- `testcases.test_ella.base_ella_test.SimpleEllaTest` 基类

### 2. 文件匹配规则
- 优先进行精确文件名匹配
- 如果精确匹配失败，使用关键词模糊匹配
- 匹配失败的query会被跳过并记录日志

### 3. 生成规则
- 类名添加 `Voice` 后缀
- 方法名添加 `_voice` 后缀
- 文件名添加 `test_voice_` 前缀
- 自动添加语音相关参数和配置

## 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 检查文件路径是否正确
   - 确认文件格式为 `.xlsx`
   - 验证文件是否包含 `query` 和 `类别` 列

2. **文本脚本匹配失败**
   - 检查对应目录下是否存在相关文本脚本
   - 验证文件命名是否符合规范
   - 查看日志中的匹配尝试信息

3. **生成的脚本格式问题**
   - 检查原始文本脚本的格式是否标准
   - 验证正则表达式匹配是否正确
   - 查看生成的脚本并手动调整

### 调试模式
可以使用测试脚本进行调试：

```bash
python temp/test_voice_generator.py
```

## 扩展功能

### 1. 自定义语音参数
可以为特定的query设置不同的语音参数：

```python
# 在generate_voice_script方法中添加条件判断
if "chinese" in query.lower():
    voice_language = "zh-CN"
elif "french" in query.lower():
    voice_language = "fr-FR"
```

### 2. 批量处理特定类别
```python
# 只处理特定类别的数据
df = generator.load_excel_data()
dialogue_data = df[df['类别'] == 'dialogue']
for _, row in dialogue_data.iterrows():
    generator.generate_voice_script(row['query'], row['类别'])
```

## 版本历史

- **v1.1**: 优化版本
  - ✅ 智能提取原始脚本的 `expected_text` 值，确保期望响应保持一致
  - ✅ 支持有类属性和无类属性的脚本模板
  - ✅ 改进正则表达式匹配，提高转换准确性
  - ✅ 优化脚本格式，避免重复属性定义

- **v1.0**: 初始版本，支持基本的语音脚本生成功能
  - 基于 `test_voice_open_camera.py` 的实现方式
  - 支持Excel数据驱动的批量生成
  - 实现智能目录映射和模板匹配
